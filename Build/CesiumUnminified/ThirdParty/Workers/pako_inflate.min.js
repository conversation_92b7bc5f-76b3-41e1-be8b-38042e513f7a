/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */
!function(e, t) {
  "object" == typeof exports && "undefined" != typeof module ? t(exports) : "function" == typeof define && define.amd ? define(["exports"], t) : t((e = "undefined" != typeof globalThis ? globalThis : e || self).pako = {});
}(this, function(e) {
  "use strict";
  var t = (e2, t2, i2, n2) => {
    let a2 = 65535 & e2 | 0, r2 = e2 >>> 16 & 65535 | 0, o2 = 0;
    for (; 0 !== i2; ) {
      o2 = i2 > 2e3 ? 2e3 : i2, i2 -= o2;
      do {
        a2 = a2 + t2[n2++] | 0, r2 = r2 + a2 | 0;
      } while (--o2);
      a2 %= 65521, r2 %= 65521;
    }
    return a2 | r2 << 16 | 0;
  };
  const i = new Uint32Array((() => {
    let e2, t2 = [];
    for (var i2 = 0; i2 < 256; i2++) {
      e2 = i2;
      for (var n2 = 0; n2 < 8; n2++) e2 = 1 & e2 ? ********** ^ e2 >>> 1 : e2 >>> 1;
      t2[i2] = e2;
    }
    return t2;
  })());
  var n = (e2, t2, n2, a2) => {
    const r2 = i, o2 = a2 + n2;
    e2 ^= -1;
    for (let i2 = a2; i2 < o2; i2++) e2 = e2 >>> 8 ^ r2[255 & (e2 ^ t2[i2])];
    return -1 ^ e2;
  };
  const a = 16209;
  var r = function(e2, t2) {
    let i2, n2, r2, o2, s2, l2, d2, f2, c2, h2, u2, w2, b2, m2, k2, _2, g2, p2, v2, x2, y2, E2, R2, A2;
    const Z2 = e2.state;
    i2 = e2.next_in, R2 = e2.input, n2 = i2 + (e2.avail_in - 5), r2 = e2.next_out, A2 = e2.output, o2 = r2 - (t2 - e2.avail_out), s2 = r2 + (e2.avail_out - 257), l2 = Z2.dmax, d2 = Z2.wsize, f2 = Z2.whave, c2 = Z2.wnext, h2 = Z2.window, u2 = Z2.hold, w2 = Z2.bits, b2 = Z2.lencode, m2 = Z2.distcode, k2 = (1 << Z2.lenbits) - 1, _2 = (1 << Z2.distbits) - 1;
    e: do {
      w2 < 15 && (u2 += R2[i2++] << w2, w2 += 8, u2 += R2[i2++] << w2, w2 += 8), g2 = b2[u2 & k2];
      t: for (; ; ) {
        if (p2 = g2 >>> 24, u2 >>>= p2, w2 -= p2, p2 = g2 >>> 16 & 255, 0 === p2) A2[r2++] = 65535 & g2;
        else {
          if (!(16 & p2)) {
            if (0 == (64 & p2)) {
              g2 = b2[(65535 & g2) + (u2 & (1 << p2) - 1)];
              continue t;
            }
            if (32 & p2) {
              Z2.mode = 16191;
              break e;
            }
            e2.msg = "invalid literal/length code", Z2.mode = a;
            break e;
          }
          v2 = 65535 & g2, p2 &= 15, p2 && (w2 < p2 && (u2 += R2[i2++] << w2, w2 += 8), v2 += u2 & (1 << p2) - 1, u2 >>>= p2, w2 -= p2), w2 < 15 && (u2 += R2[i2++] << w2, w2 += 8, u2 += R2[i2++] << w2, w2 += 8), g2 = m2[u2 & _2];
          i: for (; ; ) {
            if (p2 = g2 >>> 24, u2 >>>= p2, w2 -= p2, p2 = g2 >>> 16 & 255, !(16 & p2)) {
              if (0 == (64 & p2)) {
                g2 = m2[(65535 & g2) + (u2 & (1 << p2) - 1)];
                continue i;
              }
              e2.msg = "invalid distance code", Z2.mode = a;
              break e;
            }
            if (x2 = 65535 & g2, p2 &= 15, w2 < p2 && (u2 += R2[i2++] << w2, w2 += 8, w2 < p2 && (u2 += R2[i2++] << w2, w2 += 8)), x2 += u2 & (1 << p2) - 1, x2 > l2) {
              e2.msg = "invalid distance too far back", Z2.mode = a;
              break e;
            }
            if (u2 >>>= p2, w2 -= p2, p2 = r2 - o2, x2 > p2) {
              if (p2 = x2 - p2, p2 > f2 && Z2.sane) {
                e2.msg = "invalid distance too far back", Z2.mode = a;
                break e;
              }
              if (y2 = 0, E2 = h2, 0 === c2) {
                if (y2 += d2 - p2, p2 < v2) {
                  v2 -= p2;
                  do {
                    A2[r2++] = h2[y2++];
                  } while (--p2);
                  y2 = r2 - x2, E2 = A2;
                }
              } else if (c2 < p2) {
                if (y2 += d2 + c2 - p2, p2 -= c2, p2 < v2) {
                  v2 -= p2;
                  do {
                    A2[r2++] = h2[y2++];
                  } while (--p2);
                  if (y2 = 0, c2 < v2) {
                    p2 = c2, v2 -= p2;
                    do {
                      A2[r2++] = h2[y2++];
                    } while (--p2);
                    y2 = r2 - x2, E2 = A2;
                  }
                }
              } else if (y2 += c2 - p2, p2 < v2) {
                v2 -= p2;
                do {
                  A2[r2++] = h2[y2++];
                } while (--p2);
                y2 = r2 - x2, E2 = A2;
              }
              for (; v2 > 2; ) A2[r2++] = E2[y2++], A2[r2++] = E2[y2++], A2[r2++] = E2[y2++], v2 -= 3;
              v2 && (A2[r2++] = E2[y2++], v2 > 1 && (A2[r2++] = E2[y2++]));
            } else {
              y2 = r2 - x2;
              do {
                A2[r2++] = A2[y2++], A2[r2++] = A2[y2++], A2[r2++] = A2[y2++], v2 -= 3;
              } while (v2 > 2);
              v2 && (A2[r2++] = A2[y2++], v2 > 1 && (A2[r2++] = A2[y2++]));
            }
            break;
          }
        }
        break;
      }
    } while (i2 < n2 && r2 < s2);
    v2 = w2 >> 3, i2 -= v2, w2 -= v2 << 3, u2 &= (1 << w2) - 1, e2.next_in = i2, e2.next_out = r2, e2.avail_in = i2 < n2 ? n2 - i2 + 5 : 5 - (i2 - n2), e2.avail_out = r2 < s2 ? s2 - r2 + 257 : 257 - (r2 - s2), Z2.hold = u2, Z2.bits = w2;
  };
  const o = 15, s = new Uint16Array([3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0]), l = new Uint8Array([16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78]), d = new Uint16Array([1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 0, 0]), f = new Uint8Array([16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29, 64, 64]);
  var c = (e2, t2, i2, n2, a2, r2, c2, h2) => {
    const u2 = h2.bits;
    let w2, b2, m2, k2, _2, g2, p2 = 0, v2 = 0, x2 = 0, y2 = 0, E2 = 0, R2 = 0, A2 = 0, Z2 = 0, S2 = 0, T2 = 0, O2 = null;
    const U2 = new Uint16Array(16), D2 = new Uint16Array(16);
    let I2, B2, N2, C2 = null;
    for (p2 = 0; p2 <= o; p2++) U2[p2] = 0;
    for (v2 = 0; v2 < n2; v2++) U2[t2[i2 + v2]]++;
    for (E2 = u2, y2 = o; y2 >= 1 && 0 === U2[y2]; y2--) ;
    if (E2 > y2 && (E2 = y2), 0 === y2) return a2[r2++] = 20971520, a2[r2++] = 20971520, h2.bits = 1, 0;
    for (x2 = 1; x2 < y2 && 0 === U2[x2]; x2++) ;
    for (E2 < x2 && (E2 = x2), Z2 = 1, p2 = 1; p2 <= o; p2++) if (Z2 <<= 1, Z2 -= U2[p2], Z2 < 0) return -1;
    if (Z2 > 0 && (0 === e2 || 1 !== y2)) return -1;
    for (D2[1] = 0, p2 = 1; p2 < o; p2++) D2[p2 + 1] = D2[p2] + U2[p2];
    for (v2 = 0; v2 < n2; v2++) 0 !== t2[i2 + v2] && (c2[D2[t2[i2 + v2]]++] = v2);
    if (0 === e2 ? (O2 = C2 = c2, g2 = 20) : 1 === e2 ? (O2 = s, C2 = l, g2 = 257) : (O2 = d, C2 = f, g2 = 0), T2 = 0, v2 = 0, p2 = x2, _2 = r2, R2 = E2, A2 = 0, m2 = -1, S2 = 1 << E2, k2 = S2 - 1, 1 === e2 && S2 > 852 || 2 === e2 && S2 > 592) return 1;
    for (; ; ) {
      I2 = p2 - A2, c2[v2] + 1 < g2 ? (B2 = 0, N2 = c2[v2]) : c2[v2] >= g2 ? (B2 = C2[c2[v2] - g2], N2 = O2[c2[v2] - g2]) : (B2 = 96, N2 = 0), w2 = 1 << p2 - A2, b2 = 1 << R2, x2 = b2;
      do {
        b2 -= w2, a2[_2 + (T2 >> A2) + b2] = I2 << 24 | B2 << 16 | N2 | 0;
      } while (0 !== b2);
      for (w2 = 1 << p2 - 1; T2 & w2; ) w2 >>= 1;
      if (0 !== w2 ? (T2 &= w2 - 1, T2 += w2) : T2 = 0, v2++, 0 == --U2[p2]) {
        if (p2 === y2) break;
        p2 = t2[i2 + c2[v2]];
      }
      if (p2 > E2 && (T2 & k2) !== m2) {
        for (0 === A2 && (A2 = E2), _2 += x2, R2 = p2 - A2, Z2 = 1 << R2; R2 + A2 < y2 && (Z2 -= U2[R2 + A2], !(Z2 <= 0)); ) R2++, Z2 <<= 1;
        if (S2 += 1 << R2, 1 === e2 && S2 > 852 || 2 === e2 && S2 > 592) return 1;
        m2 = T2 & k2, a2[m2] = E2 << 24 | R2 << 16 | _2 - r2 | 0;
      }
    }
    return 0 !== T2 && (a2[_2 + T2] = p2 - A2 << 24 | 64 << 16 | 0), h2.bits = E2, 0;
  }, h = { Z_NO_FLUSH: 0, Z_PARTIAL_FLUSH: 1, Z_SYNC_FLUSH: 2, Z_FULL_FLUSH: 3, Z_FINISH: 4, Z_BLOCK: 5, Z_TREES: 6, Z_OK: 0, Z_STREAM_END: 1, Z_NEED_DICT: 2, Z_ERRNO: -1, Z_STREAM_ERROR: -2, Z_DATA_ERROR: -3, Z_MEM_ERROR: -4, Z_BUF_ERROR: -5, Z_NO_COMPRESSION: 0, Z_BEST_SPEED: 1, Z_BEST_COMPRESSION: 9, Z_DEFAULT_COMPRESSION: -1, Z_FILTERED: 1, Z_HUFFMAN_ONLY: 2, Z_RLE: 3, Z_FIXED: 4, Z_DEFAULT_STRATEGY: 0, Z_BINARY: 0, Z_TEXT: 1, Z_UNKNOWN: 2, Z_DEFLATED: 8 };
  const { Z_FINISH: u, Z_BLOCK: w, Z_TREES: b, Z_OK: m, Z_STREAM_END: k, Z_NEED_DICT: _, Z_STREAM_ERROR: g, Z_DATA_ERROR: p, Z_MEM_ERROR: v, Z_BUF_ERROR: x, Z_DEFLATED: y } = h, E = 16180, R = 16190, A = 16191, Z = 16192, S = 16194, T = 16199, O = 16200, U = 16206, D = 16209, I = (e2) => (e2 >>> 24 & 255) + (e2 >>> 8 & 65280) + ((65280 & e2) << 8) + ((255 & e2) << 24);
  function B() {
    this.strm = null, this.mode = 0, this.last = false, this.wrap = 0, this.havedict = false, this.flags = 0, this.dmax = 0, this.check = 0, this.total = 0, this.head = null, this.wbits = 0, this.wsize = 0, this.whave = 0, this.wnext = 0, this.window = null, this.hold = 0, this.bits = 0, this.length = 0, this.offset = 0, this.extra = 0, this.lencode = null, this.distcode = null, this.lenbits = 0, this.distbits = 0, this.ncode = 0, this.nlen = 0, this.ndist = 0, this.have = 0, this.next = null, this.lens = new Uint16Array(320), this.work = new Uint16Array(288), this.lendyn = null, this.distdyn = null, this.sane = 0, this.back = 0, this.was = 0;
  }
  const N = (e2) => {
    if (!e2) return 1;
    const t2 = e2.state;
    return !t2 || t2.strm !== e2 || t2.mode < E || t2.mode > 16211 ? 1 : 0;
  }, C = (e2) => {
    if (N(e2)) return g;
    const t2 = e2.state;
    return e2.total_in = e2.total_out = t2.total = 0, e2.msg = "", t2.wrap && (e2.adler = 1 & t2.wrap), t2.mode = E, t2.last = 0, t2.havedict = 0, t2.flags = -1, t2.dmax = 32768, t2.head = null, t2.hold = 0, t2.bits = 0, t2.lencode = t2.lendyn = new Int32Array(852), t2.distcode = t2.distdyn = new Int32Array(592), t2.sane = 1, t2.back = -1, m;
  }, z = (e2) => {
    if (N(e2)) return g;
    const t2 = e2.state;
    return t2.wsize = 0, t2.whave = 0, t2.wnext = 0, C(e2);
  }, F = (e2, t2) => {
    let i2;
    if (N(e2)) return g;
    const n2 = e2.state;
    return t2 < 0 ? (i2 = 0, t2 = -t2) : (i2 = 5 + (t2 >> 4), t2 < 48 && (t2 &= 15)), t2 && (t2 < 8 || t2 > 15) ? g : (null !== n2.window && n2.wbits !== t2 && (n2.window = null), n2.wrap = i2, n2.wbits = t2, z(e2));
  }, L = (e2, t2) => {
    if (!e2) return g;
    const i2 = new B();
    e2.state = i2, i2.strm = e2, i2.window = null, i2.mode = E;
    const n2 = F(e2, t2);
    return n2 !== m && (e2.state = null), n2;
  };
  let M, H, j = true;
  const K = (e2) => {
    if (j) {
      M = new Int32Array(512), H = new Int32Array(32);
      let t2 = 0;
      for (; t2 < 144; ) e2.lens[t2++] = 8;
      for (; t2 < 256; ) e2.lens[t2++] = 9;
      for (; t2 < 280; ) e2.lens[t2++] = 7;
      for (; t2 < 288; ) e2.lens[t2++] = 8;
      for (c(1, e2.lens, 0, 288, M, 0, e2.work, { bits: 9 }), t2 = 0; t2 < 32; ) e2.lens[t2++] = 5;
      c(2, e2.lens, 0, 32, H, 0, e2.work, { bits: 5 }), j = false;
    }
    e2.lencode = M, e2.lenbits = 9, e2.distcode = H, e2.distbits = 5;
  }, P = (e2, t2, i2, n2) => {
    let a2;
    const r2 = e2.state;
    return null === r2.window && (r2.wsize = 1 << r2.wbits, r2.wnext = 0, r2.whave = 0, r2.window = new Uint8Array(r2.wsize)), n2 >= r2.wsize ? (r2.window.set(t2.subarray(i2 - r2.wsize, i2), 0), r2.wnext = 0, r2.whave = r2.wsize) : (a2 = r2.wsize - r2.wnext, a2 > n2 && (a2 = n2), r2.window.set(t2.subarray(i2 - n2, i2 - n2 + a2), r2.wnext), (n2 -= a2) ? (r2.window.set(t2.subarray(i2 - n2, i2), 0), r2.wnext = n2, r2.whave = r2.wsize) : (r2.wnext += a2, r2.wnext === r2.wsize && (r2.wnext = 0), r2.whave < r2.wsize && (r2.whave += a2))), 0;
  };
  var Y = { inflateReset: z, inflateReset2: F, inflateResetKeep: C, inflateInit: (e2) => L(e2, 15), inflateInit2: L, inflate: (e2, i2) => {
    let a2, o2, s2, l2, d2, f2, h2, B2, C2, z2, F2, L2, M2, H2, j2, Y2, G2, X2, W2, q2, J2, Q2, V2 = 0;
    const $2 = new Uint8Array(4);
    let ee2, te2;
    const ie2 = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);
    if (N(e2) || !e2.output || !e2.input && 0 !== e2.avail_in) return g;
    a2 = e2.state, a2.mode === A && (a2.mode = Z), d2 = e2.next_out, s2 = e2.output, h2 = e2.avail_out, l2 = e2.next_in, o2 = e2.input, f2 = e2.avail_in, B2 = a2.hold, C2 = a2.bits, z2 = f2, F2 = h2, Q2 = m;
    e: for (; ; ) switch (a2.mode) {
      case E:
        if (0 === a2.wrap) {
          a2.mode = Z;
          break;
        }
        for (; C2 < 16; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if (2 & a2.wrap && 35615 === B2) {
          0 === a2.wbits && (a2.wbits = 15), a2.check = 0, $2[0] = 255 & B2, $2[1] = B2 >>> 8 & 255, a2.check = n(a2.check, $2, 2, 0), B2 = 0, C2 = 0, a2.mode = 16181;
          break;
        }
        if (a2.head && (a2.head.done = false), !(1 & a2.wrap) || (((255 & B2) << 8) + (B2 >> 8)) % 31) {
          e2.msg = "incorrect header check", a2.mode = D;
          break;
        }
        if ((15 & B2) !== y) {
          e2.msg = "unknown compression method", a2.mode = D;
          break;
        }
        if (B2 >>>= 4, C2 -= 4, J2 = 8 + (15 & B2), 0 === a2.wbits && (a2.wbits = J2), J2 > 15 || J2 > a2.wbits) {
          e2.msg = "invalid window size", a2.mode = D;
          break;
        }
        a2.dmax = 1 << a2.wbits, a2.flags = 0, e2.adler = a2.check = 1, a2.mode = 512 & B2 ? 16189 : A, B2 = 0, C2 = 0;
        break;
      case 16181:
        for (; C2 < 16; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if (a2.flags = B2, (255 & a2.flags) !== y) {
          e2.msg = "unknown compression method", a2.mode = D;
          break;
        }
        if (57344 & a2.flags) {
          e2.msg = "unknown header flags set", a2.mode = D;
          break;
        }
        a2.head && (a2.head.text = B2 >> 8 & 1), 512 & a2.flags && 4 & a2.wrap && ($2[0] = 255 & B2, $2[1] = B2 >>> 8 & 255, a2.check = n(a2.check, $2, 2, 0)), B2 = 0, C2 = 0, a2.mode = 16182;
      case 16182:
        for (; C2 < 32; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        a2.head && (a2.head.time = B2), 512 & a2.flags && 4 & a2.wrap && ($2[0] = 255 & B2, $2[1] = B2 >>> 8 & 255, $2[2] = B2 >>> 16 & 255, $2[3] = B2 >>> 24 & 255, a2.check = n(a2.check, $2, 4, 0)), B2 = 0, C2 = 0, a2.mode = 16183;
      case 16183:
        for (; C2 < 16; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        a2.head && (a2.head.xflags = 255 & B2, a2.head.os = B2 >> 8), 512 & a2.flags && 4 & a2.wrap && ($2[0] = 255 & B2, $2[1] = B2 >>> 8 & 255, a2.check = n(a2.check, $2, 2, 0)), B2 = 0, C2 = 0, a2.mode = 16184;
      case 16184:
        if (1024 & a2.flags) {
          for (; C2 < 16; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          a2.length = B2, a2.head && (a2.head.extra_len = B2), 512 & a2.flags && 4 & a2.wrap && ($2[0] = 255 & B2, $2[1] = B2 >>> 8 & 255, a2.check = n(a2.check, $2, 2, 0)), B2 = 0, C2 = 0;
        } else a2.head && (a2.head.extra = null);
        a2.mode = 16185;
      case 16185:
        if (1024 & a2.flags && (L2 = a2.length, L2 > f2 && (L2 = f2), L2 && (a2.head && (J2 = a2.head.extra_len - a2.length, a2.head.extra || (a2.head.extra = new Uint8Array(a2.head.extra_len)), a2.head.extra.set(o2.subarray(l2, l2 + L2), J2)), 512 & a2.flags && 4 & a2.wrap && (a2.check = n(a2.check, o2, L2, l2)), f2 -= L2, l2 += L2, a2.length -= L2), a2.length)) break e;
        a2.length = 0, a2.mode = 16186;
      case 16186:
        if (2048 & a2.flags) {
          if (0 === f2) break e;
          L2 = 0;
          do {
            J2 = o2[l2 + L2++], a2.head && J2 && a2.length < 65536 && (a2.head.name += String.fromCharCode(J2));
          } while (J2 && L2 < f2);
          if (512 & a2.flags && 4 & a2.wrap && (a2.check = n(a2.check, o2, L2, l2)), f2 -= L2, l2 += L2, J2) break e;
        } else a2.head && (a2.head.name = null);
        a2.length = 0, a2.mode = 16187;
      case 16187:
        if (4096 & a2.flags) {
          if (0 === f2) break e;
          L2 = 0;
          do {
            J2 = o2[l2 + L2++], a2.head && J2 && a2.length < 65536 && (a2.head.comment += String.fromCharCode(J2));
          } while (J2 && L2 < f2);
          if (512 & a2.flags && 4 & a2.wrap && (a2.check = n(a2.check, o2, L2, l2)), f2 -= L2, l2 += L2, J2) break e;
        } else a2.head && (a2.head.comment = null);
        a2.mode = 16188;
      case 16188:
        if (512 & a2.flags) {
          for (; C2 < 16; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          if (4 & a2.wrap && B2 !== (65535 & a2.check)) {
            e2.msg = "header crc mismatch", a2.mode = D;
            break;
          }
          B2 = 0, C2 = 0;
        }
        a2.head && (a2.head.hcrc = a2.flags >> 9 & 1, a2.head.done = true), e2.adler = a2.check = 0, a2.mode = A;
        break;
      case 16189:
        for (; C2 < 32; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        e2.adler = a2.check = I(B2), B2 = 0, C2 = 0, a2.mode = R;
      case R:
        if (0 === a2.havedict) return e2.next_out = d2, e2.avail_out = h2, e2.next_in = l2, e2.avail_in = f2, a2.hold = B2, a2.bits = C2, _;
        e2.adler = a2.check = 1, a2.mode = A;
      case A:
        if (i2 === w || i2 === b) break e;
      case Z:
        if (a2.last) {
          B2 >>>= 7 & C2, C2 -= 7 & C2, a2.mode = U;
          break;
        }
        for (; C2 < 3; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        switch (a2.last = 1 & B2, B2 >>>= 1, C2 -= 1, 3 & B2) {
          case 0:
            a2.mode = 16193;
            break;
          case 1:
            if (K(a2), a2.mode = T, i2 === b) {
              B2 >>>= 2, C2 -= 2;
              break e;
            }
            break;
          case 2:
            a2.mode = 16196;
            break;
          case 3:
            e2.msg = "invalid block type", a2.mode = D;
        }
        B2 >>>= 2, C2 -= 2;
        break;
      case 16193:
        for (B2 >>>= 7 & C2, C2 -= 7 & C2; C2 < 32; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if ((65535 & B2) != (B2 >>> 16 ^ 65535)) {
          e2.msg = "invalid stored block lengths", a2.mode = D;
          break;
        }
        if (a2.length = 65535 & B2, B2 = 0, C2 = 0, a2.mode = S, i2 === b) break e;
      case S:
        a2.mode = 16195;
      case 16195:
        if (L2 = a2.length, L2) {
          if (L2 > f2 && (L2 = f2), L2 > h2 && (L2 = h2), 0 === L2) break e;
          s2.set(o2.subarray(l2, l2 + L2), d2), f2 -= L2, l2 += L2, h2 -= L2, d2 += L2, a2.length -= L2;
          break;
        }
        a2.mode = A;
        break;
      case 16196:
        for (; C2 < 14; ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if (a2.nlen = 257 + (31 & B2), B2 >>>= 5, C2 -= 5, a2.ndist = 1 + (31 & B2), B2 >>>= 5, C2 -= 5, a2.ncode = 4 + (15 & B2), B2 >>>= 4, C2 -= 4, a2.nlen > 286 || a2.ndist > 30) {
          e2.msg = "too many length or distance symbols", a2.mode = D;
          break;
        }
        a2.have = 0, a2.mode = 16197;
      case 16197:
        for (; a2.have < a2.ncode; ) {
          for (; C2 < 3; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          a2.lens[ie2[a2.have++]] = 7 & B2, B2 >>>= 3, C2 -= 3;
        }
        for (; a2.have < 19; ) a2.lens[ie2[a2.have++]] = 0;
        if (a2.lencode = a2.lendyn, a2.lenbits = 7, ee2 = { bits: a2.lenbits }, Q2 = c(0, a2.lens, 0, 19, a2.lencode, 0, a2.work, ee2), a2.lenbits = ee2.bits, Q2) {
          e2.msg = "invalid code lengths set", a2.mode = D;
          break;
        }
        a2.have = 0, a2.mode = 16198;
      case 16198:
        for (; a2.have < a2.nlen + a2.ndist; ) {
          for (; V2 = a2.lencode[B2 & (1 << a2.lenbits) - 1], j2 = V2 >>> 24, Y2 = V2 >>> 16 & 255, G2 = 65535 & V2, !(j2 <= C2); ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          if (G2 < 16) B2 >>>= j2, C2 -= j2, a2.lens[a2.have++] = G2;
          else {
            if (16 === G2) {
              for (te2 = j2 + 2; C2 < te2; ) {
                if (0 === f2) break e;
                f2--, B2 += o2[l2++] << C2, C2 += 8;
              }
              if (B2 >>>= j2, C2 -= j2, 0 === a2.have) {
                e2.msg = "invalid bit length repeat", a2.mode = D;
                break;
              }
              J2 = a2.lens[a2.have - 1], L2 = 3 + (3 & B2), B2 >>>= 2, C2 -= 2;
            } else if (17 === G2) {
              for (te2 = j2 + 3; C2 < te2; ) {
                if (0 === f2) break e;
                f2--, B2 += o2[l2++] << C2, C2 += 8;
              }
              B2 >>>= j2, C2 -= j2, J2 = 0, L2 = 3 + (7 & B2), B2 >>>= 3, C2 -= 3;
            } else {
              for (te2 = j2 + 7; C2 < te2; ) {
                if (0 === f2) break e;
                f2--, B2 += o2[l2++] << C2, C2 += 8;
              }
              B2 >>>= j2, C2 -= j2, J2 = 0, L2 = 11 + (127 & B2), B2 >>>= 7, C2 -= 7;
            }
            if (a2.have + L2 > a2.nlen + a2.ndist) {
              e2.msg = "invalid bit length repeat", a2.mode = D;
              break;
            }
            for (; L2--; ) a2.lens[a2.have++] = J2;
          }
        }
        if (a2.mode === D) break;
        if (0 === a2.lens[256]) {
          e2.msg = "invalid code -- missing end-of-block", a2.mode = D;
          break;
        }
        if (a2.lenbits = 9, ee2 = { bits: a2.lenbits }, Q2 = c(1, a2.lens, 0, a2.nlen, a2.lencode, 0, a2.work, ee2), a2.lenbits = ee2.bits, Q2) {
          e2.msg = "invalid literal/lengths set", a2.mode = D;
          break;
        }
        if (a2.distbits = 6, a2.distcode = a2.distdyn, ee2 = { bits: a2.distbits }, Q2 = c(2, a2.lens, a2.nlen, a2.ndist, a2.distcode, 0, a2.work, ee2), a2.distbits = ee2.bits, Q2) {
          e2.msg = "invalid distances set", a2.mode = D;
          break;
        }
        if (a2.mode = T, i2 === b) break e;
      case T:
        a2.mode = O;
      case O:
        if (f2 >= 6 && h2 >= 258) {
          e2.next_out = d2, e2.avail_out = h2, e2.next_in = l2, e2.avail_in = f2, a2.hold = B2, a2.bits = C2, r(e2, F2), d2 = e2.next_out, s2 = e2.output, h2 = e2.avail_out, l2 = e2.next_in, o2 = e2.input, f2 = e2.avail_in, B2 = a2.hold, C2 = a2.bits, a2.mode === A && (a2.back = -1);
          break;
        }
        for (a2.back = 0; V2 = a2.lencode[B2 & (1 << a2.lenbits) - 1], j2 = V2 >>> 24, Y2 = V2 >>> 16 & 255, G2 = 65535 & V2, !(j2 <= C2); ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if (Y2 && 0 == (240 & Y2)) {
          for (X2 = j2, W2 = Y2, q2 = G2; V2 = a2.lencode[q2 + ((B2 & (1 << X2 + W2) - 1) >> X2)], j2 = V2 >>> 24, Y2 = V2 >>> 16 & 255, G2 = 65535 & V2, !(X2 + j2 <= C2); ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          B2 >>>= X2, C2 -= X2, a2.back += X2;
        }
        if (B2 >>>= j2, C2 -= j2, a2.back += j2, a2.length = G2, 0 === Y2) {
          a2.mode = 16205;
          break;
        }
        if (32 & Y2) {
          a2.back = -1, a2.mode = A;
          break;
        }
        if (64 & Y2) {
          e2.msg = "invalid literal/length code", a2.mode = D;
          break;
        }
        a2.extra = 15 & Y2, a2.mode = 16201;
      case 16201:
        if (a2.extra) {
          for (te2 = a2.extra; C2 < te2; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          a2.length += B2 & (1 << a2.extra) - 1, B2 >>>= a2.extra, C2 -= a2.extra, a2.back += a2.extra;
        }
        a2.was = a2.length, a2.mode = 16202;
      case 16202:
        for (; V2 = a2.distcode[B2 & (1 << a2.distbits) - 1], j2 = V2 >>> 24, Y2 = V2 >>> 16 & 255, G2 = 65535 & V2, !(j2 <= C2); ) {
          if (0 === f2) break e;
          f2--, B2 += o2[l2++] << C2, C2 += 8;
        }
        if (0 == (240 & Y2)) {
          for (X2 = j2, W2 = Y2, q2 = G2; V2 = a2.distcode[q2 + ((B2 & (1 << X2 + W2) - 1) >> X2)], j2 = V2 >>> 24, Y2 = V2 >>> 16 & 255, G2 = 65535 & V2, !(X2 + j2 <= C2); ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          B2 >>>= X2, C2 -= X2, a2.back += X2;
        }
        if (B2 >>>= j2, C2 -= j2, a2.back += j2, 64 & Y2) {
          e2.msg = "invalid distance code", a2.mode = D;
          break;
        }
        a2.offset = G2, a2.extra = 15 & Y2, a2.mode = 16203;
      case 16203:
        if (a2.extra) {
          for (te2 = a2.extra; C2 < te2; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          a2.offset += B2 & (1 << a2.extra) - 1, B2 >>>= a2.extra, C2 -= a2.extra, a2.back += a2.extra;
        }
        if (a2.offset > a2.dmax) {
          e2.msg = "invalid distance too far back", a2.mode = D;
          break;
        }
        a2.mode = 16204;
      case 16204:
        if (0 === h2) break e;
        if (L2 = F2 - h2, a2.offset > L2) {
          if (L2 = a2.offset - L2, L2 > a2.whave && a2.sane) {
            e2.msg = "invalid distance too far back", a2.mode = D;
            break;
          }
          L2 > a2.wnext ? (L2 -= a2.wnext, M2 = a2.wsize - L2) : M2 = a2.wnext - L2, L2 > a2.length && (L2 = a2.length), H2 = a2.window;
        } else H2 = s2, M2 = d2 - a2.offset, L2 = a2.length;
        L2 > h2 && (L2 = h2), h2 -= L2, a2.length -= L2;
        do {
          s2[d2++] = H2[M2++];
        } while (--L2);
        0 === a2.length && (a2.mode = O);
        break;
      case 16205:
        if (0 === h2) break e;
        s2[d2++] = a2.length, h2--, a2.mode = O;
        break;
      case U:
        if (a2.wrap) {
          for (; C2 < 32; ) {
            if (0 === f2) break e;
            f2--, B2 |= o2[l2++] << C2, C2 += 8;
          }
          if (F2 -= h2, e2.total_out += F2, a2.total += F2, 4 & a2.wrap && F2 && (e2.adler = a2.check = a2.flags ? n(a2.check, s2, F2, d2 - F2) : t(a2.check, s2, F2, d2 - F2)), F2 = h2, 4 & a2.wrap && (a2.flags ? B2 : I(B2)) !== a2.check) {
            e2.msg = "incorrect data check", a2.mode = D;
            break;
          }
          B2 = 0, C2 = 0;
        }
        a2.mode = 16207;
      case 16207:
        if (a2.wrap && a2.flags) {
          for (; C2 < 32; ) {
            if (0 === f2) break e;
            f2--, B2 += o2[l2++] << C2, C2 += 8;
          }
          if (4 & a2.wrap && B2 !== (4294967295 & a2.total)) {
            e2.msg = "incorrect length check", a2.mode = D;
            break;
          }
          B2 = 0, C2 = 0;
        }
        a2.mode = 16208;
      case 16208:
        Q2 = k;
        break e;
      case D:
        Q2 = p;
        break e;
      case 16210:
        return v;
      default:
        return g;
    }
    return e2.next_out = d2, e2.avail_out = h2, e2.next_in = l2, e2.avail_in = f2, a2.hold = B2, a2.bits = C2, (a2.wsize || F2 !== e2.avail_out && a2.mode < D && (a2.mode < U || i2 !== u)) && P(e2, e2.output, e2.next_out, F2 - e2.avail_out), z2 -= e2.avail_in, F2 -= e2.avail_out, e2.total_in += z2, e2.total_out += F2, a2.total += F2, 4 & a2.wrap && F2 && (e2.adler = a2.check = a2.flags ? n(a2.check, s2, F2, e2.next_out - F2) : t(a2.check, s2, F2, e2.next_out - F2)), e2.data_type = a2.bits + (a2.last ? 64 : 0) + (a2.mode === A ? 128 : 0) + (a2.mode === T || a2.mode === S ? 256 : 0), (0 === z2 && 0 === F2 || i2 === u) && Q2 === m && (Q2 = x), Q2;
  }, inflateEnd: (e2) => {
    if (N(e2)) return g;
    let t2 = e2.state;
    return t2.window && (t2.window = null), e2.state = null, m;
  }, inflateGetHeader: (e2, t2) => {
    if (N(e2)) return g;
    const i2 = e2.state;
    return 0 == (2 & i2.wrap) ? g : (i2.head = t2, t2.done = false, m);
  }, inflateSetDictionary: (e2, i2) => {
    const n2 = i2.length;
    let a2, r2, o2;
    return N(e2) ? g : (a2 = e2.state, 0 !== a2.wrap && a2.mode !== R ? g : a2.mode === R && (r2 = 1, r2 = t(r2, i2, n2, 0), r2 !== a2.check) ? p : (o2 = P(e2, i2, n2, n2), o2 ? (a2.mode = 16210, v) : (a2.havedict = 1, m)));
  }, inflateInfo: "pako inflate (from Nodeca project)" };
  const G = (e2, t2) => Object.prototype.hasOwnProperty.call(e2, t2);
  var X = function(e2) {
    const t2 = Array.prototype.slice.call(arguments, 1);
    for (; t2.length; ) {
      const i2 = t2.shift();
      if (i2) {
        if ("object" != typeof i2) throw new TypeError(i2 + "must be non-object");
        for (const t3 in i2) G(i2, t3) && (e2[t3] = i2[t3]);
      }
    }
    return e2;
  }, W = (e2) => {
    let t2 = 0;
    for (let i3 = 0, n2 = e2.length; i3 < n2; i3++) t2 += e2[i3].length;
    const i2 = new Uint8Array(t2);
    for (let t3 = 0, n2 = 0, a2 = e2.length; t3 < a2; t3++) {
      let a3 = e2[t3];
      i2.set(a3, n2), n2 += a3.length;
    }
    return i2;
  };
  let q = true;
  try {
    String.fromCharCode.apply(null, new Uint8Array(1));
  } catch (e2) {
    q = false;
  }
  const J = new Uint8Array(256);
  for (let e2 = 0; e2 < 256; e2++) J[e2] = e2 >= 252 ? 6 : e2 >= 248 ? 5 : e2 >= 240 ? 4 : e2 >= 224 ? 3 : e2 >= 192 ? 2 : 1;
  J[254] = J[254] = 1;
  var Q = (e2) => {
    if ("function" == typeof TextEncoder && TextEncoder.prototype.encode) return new TextEncoder().encode(e2);
    let t2, i2, n2, a2, r2, o2 = e2.length, s2 = 0;
    for (a2 = 0; a2 < o2; a2++) i2 = e2.charCodeAt(a2), 55296 == (64512 & i2) && a2 + 1 < o2 && (n2 = e2.charCodeAt(a2 + 1), 56320 == (64512 & n2) && (i2 = 65536 + (i2 - 55296 << 10) + (n2 - 56320), a2++)), s2 += i2 < 128 ? 1 : i2 < 2048 ? 2 : i2 < 65536 ? 3 : 4;
    for (t2 = new Uint8Array(s2), r2 = 0, a2 = 0; r2 < s2; a2++) i2 = e2.charCodeAt(a2), 55296 == (64512 & i2) && a2 + 1 < o2 && (n2 = e2.charCodeAt(a2 + 1), 56320 == (64512 & n2) && (i2 = 65536 + (i2 - 55296 << 10) + (n2 - 56320), a2++)), i2 < 128 ? t2[r2++] = i2 : i2 < 2048 ? (t2[r2++] = 192 | i2 >>> 6, t2[r2++] = 128 | 63 & i2) : i2 < 65536 ? (t2[r2++] = 224 | i2 >>> 12, t2[r2++] = 128 | i2 >>> 6 & 63, t2[r2++] = 128 | 63 & i2) : (t2[r2++] = 240 | i2 >>> 18, t2[r2++] = 128 | i2 >>> 12 & 63, t2[r2++] = 128 | i2 >>> 6 & 63, t2[r2++] = 128 | 63 & i2);
    return t2;
  }, V = (e2, t2) => {
    const i2 = t2 || e2.length;
    if ("function" == typeof TextDecoder && TextDecoder.prototype.decode) return new TextDecoder().decode(e2.subarray(0, t2));
    let n2, a2;
    const r2 = new Array(2 * i2);
    for (a2 = 0, n2 = 0; n2 < i2; ) {
      let t3 = e2[n2++];
      if (t3 < 128) {
        r2[a2++] = t3;
        continue;
      }
      let o2 = J[t3];
      if (o2 > 4) r2[a2++] = 65533, n2 += o2 - 1;
      else {
        for (t3 &= 2 === o2 ? 31 : 3 === o2 ? 15 : 7; o2 > 1 && n2 < i2; ) t3 = t3 << 6 | 63 & e2[n2++], o2--;
        o2 > 1 ? r2[a2++] = 65533 : t3 < 65536 ? r2[a2++] = t3 : (t3 -= 65536, r2[a2++] = 55296 | t3 >> 10 & 1023, r2[a2++] = 56320 | 1023 & t3);
      }
    }
    return ((e3, t3) => {
      if (t3 < 65534 && e3.subarray && q) return String.fromCharCode.apply(null, e3.length === t3 ? e3 : e3.subarray(0, t3));
      let i3 = "";
      for (let n3 = 0; n3 < t3; n3++) i3 += String.fromCharCode(e3[n3]);
      return i3;
    })(r2, a2);
  }, $ = (e2, t2) => {
    (t2 = t2 || e2.length) > e2.length && (t2 = e2.length);
    let i2 = t2 - 1;
    for (; i2 >= 0 && 128 == (192 & e2[i2]); ) i2--;
    return i2 < 0 || 0 === i2 ? t2 : i2 + J[e2[i2]] > t2 ? i2 : t2;
  }, ee = { 2: "need dictionary", 1: "stream end", 0: "", "-1": "file error", "-2": "stream error", "-3": "data error", "-4": "insufficient memory", "-5": "buffer error", "-6": "incompatible version" };
  var te = function() {
    this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0;
  };
  var ie = function() {
    this.text = 0, this.time = 0, this.xflags = 0, this.os = 0, this.extra = null, this.extra_len = 0, this.name = "", this.comment = "", this.hcrc = 0, this.done = false;
  };
  const ne = Object.prototype.toString, { Z_NO_FLUSH: ae, Z_FINISH: re, Z_OK: oe, Z_STREAM_END: se, Z_NEED_DICT: le, Z_STREAM_ERROR: de, Z_DATA_ERROR: fe, Z_MEM_ERROR: ce } = h;
  function he(e2) {
    this.options = X({ chunkSize: 65536, windowBits: 15, to: "" }, e2 || {});
    const t2 = this.options;
    t2.raw && t2.windowBits >= 0 && t2.windowBits < 16 && (t2.windowBits = -t2.windowBits, 0 === t2.windowBits && (t2.windowBits = -15)), !(t2.windowBits >= 0 && t2.windowBits < 16) || e2 && e2.windowBits || (t2.windowBits += 32), t2.windowBits > 15 && t2.windowBits < 48 && 0 == (15 & t2.windowBits) && (t2.windowBits |= 15), this.err = 0, this.msg = "", this.ended = false, this.chunks = [], this.strm = new te(), this.strm.avail_out = 0;
    let i2 = Y.inflateInit2(this.strm, t2.windowBits);
    if (i2 !== oe) throw new Error(ee[i2]);
    if (this.header = new ie(), Y.inflateGetHeader(this.strm, this.header), t2.dictionary && ("string" == typeof t2.dictionary ? t2.dictionary = Q(t2.dictionary) : "[object ArrayBuffer]" === ne.call(t2.dictionary) && (t2.dictionary = new Uint8Array(t2.dictionary)), t2.raw && (i2 = Y.inflateSetDictionary(this.strm, t2.dictionary), i2 !== oe))) throw new Error(ee[i2]);
  }
  function ue(e2, t2) {
    const i2 = new he(t2);
    if (i2.push(e2), i2.err) throw i2.msg || ee[i2.err];
    return i2.result;
  }
  he.prototype.push = function(e2, t2) {
    const i2 = this.strm, n2 = this.options.chunkSize, a2 = this.options.dictionary;
    let r2, o2, s2;
    if (this.ended) return false;
    for (o2 = t2 === ~~t2 ? t2 : true === t2 ? re : ae, "[object ArrayBuffer]" === ne.call(e2) ? i2.input = new Uint8Array(e2) : i2.input = e2, i2.next_in = 0, i2.avail_in = i2.input.length; ; ) {
      for (0 === i2.avail_out && (i2.output = new Uint8Array(n2), i2.next_out = 0, i2.avail_out = n2), r2 = Y.inflate(i2, o2), r2 === le && a2 && (r2 = Y.inflateSetDictionary(i2, a2), r2 === oe ? r2 = Y.inflate(i2, o2) : r2 === fe && (r2 = le)); i2.avail_in > 0 && r2 === se && i2.state.wrap > 0 && 0 !== e2[i2.next_in]; ) Y.inflateReset(i2), r2 = Y.inflate(i2, o2);
      switch (r2) {
        case de:
        case fe:
        case le:
        case ce:
          return this.onEnd(r2), this.ended = true, false;
      }
      if (s2 = i2.avail_out, i2.next_out && (0 === i2.avail_out || r2 === se)) if ("string" === this.options.to) {
        let e3 = $(i2.output, i2.next_out), t3 = i2.next_out - e3, a3 = V(i2.output, e3);
        i2.next_out = t3, i2.avail_out = n2 - t3, t3 && i2.output.set(i2.output.subarray(e3, e3 + t3), 0), this.onData(a3);
      } else this.onData(i2.output.length === i2.next_out ? i2.output : i2.output.subarray(0, i2.next_out));
      if (r2 !== oe || 0 !== s2) {
        if (r2 === se) return r2 = Y.inflateEnd(this.strm), this.onEnd(r2), this.ended = true, true;
        if (0 === i2.avail_in) break;
      }
    }
    return true;
  }, he.prototype.onData = function(e2) {
    this.chunks.push(e2);
  }, he.prototype.onEnd = function(e2) {
    e2 === oe && ("string" === this.options.to ? this.result = this.chunks.join("") : this.result = W(this.chunks)), this.chunks = [], this.err = e2, this.msg = this.strm.msg;
  };
  var we = he, be = ue, me = function(e2, t2) {
    return (t2 = t2 || {}).raw = true, ue(e2, t2);
  }, ke = ue, _e = h, ge = { Inflate: we, inflate: be, inflateRaw: me, ungzip: ke, constants: _e };
  e.Inflate = we, e.constants = _e, e.default = ge, e.inflate = be, e.inflateRaw = me, e.ungzip = ke, Object.defineProperty(e, "__esModule", { value: true });
});
