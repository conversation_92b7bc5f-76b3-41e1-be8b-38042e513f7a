/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as G}from"./chunk-IQ3P7OYE.js";import{a as At}from"./chunk-6NNCMAGT.js";import{a as yt}from"./chunk-X76FJHF4.js";import{a as $}from"./chunk-O4MLFQKO.js";import{a as v}from"./chunk-KA7ZX4BQ.js";import{a as st}from"./chunk-PFXLBIMV.js";import{a as ht}from"./chunk-PSBTKXXJ.js";import{b as ct,c as rt,d as F}from"./chunk-UJOKCDQH.js";import{a as lt,d as it}from"./chunk-KYREMICR.js";import{f as nt,h as bt}from"./chunk-VBRVI5XI.js";import{a as P}from"./chunk-5ZFOKSDK.js";import{a,b as _t,c as Q,d as D,e as C,f as dt}from"./chunk-7L2LUDC3.js";import{a as R}from"./chunk-77KFIUJG.js";import{a as ot,b as k}from"./chunk-PX3QTMVS.js";import{e as Z}from"./chunk-FE4HG5RY.js";var tt=new a,pt=new a,xt=new a,wt=new a,w=new Q,Mt=new C,Vt=new C,gt=new nt,Tt=new a,Nt=new a,Et=new a,ft=new _t,Pt=new a,Ft=new Q,St=new Q;function Ot(o,e,t){let n=e.vertexFormat,s=e.center,i=e.semiMajorAxis,r=e.semiMinorAxis,f=e.ellipsoid,h=e.stRotation,N=t?o.length/3*2:o.length/3,g=e.shadowVolume,c=n.st?new Float32Array(N*2):void 0,l=n.normal?new Float32Array(N*3):void 0,A=n.tangent?new Float32Array(N*3):void 0,x=n.bitangent?new Float32Array(N*3):void 0,S=g?new Float32Array(N*3):void 0,z=0,b=Tt,M=Nt,d=Et,_=new lt(f),I=_.project(f.cartesianToCartographic(s,ft),Pt),W=f.scaleToGeodeticSurface(s,tt);f.geodeticSurfaceNormal(W,W);let J=Mt,U=Vt;if(h!==0){let m=nt.fromAxisAngle(W,h,gt);J=C.fromQuaternion(m,J),m=nt.fromAxisAngle(W,-h,gt),U=C.fromQuaternion(m,U)}else J=C.clone(C.IDENTITY,J),U=C.clone(C.IDENTITY,U);let B=Q.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Ft),q=Q.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,St),O=o.length,y=t?O:0,p=y/3*2;for(let m=0;m<O;m+=3){let u=m+1,T=m+2,V=a.fromArray(o,m,tt);if(n.st){let E=C.multiplyByVector(J,V,pt),j=_.project(f.cartesianToCartographic(E,ft),xt);a.subtract(j,I,j),w.x=(j.x+i)/(2*i),w.y=(j.y+r)/(2*r),B.x=Math.min(w.x,B.x),B.y=Math.min(w.y,B.y),q.x=Math.max(w.x,q.x),q.y=Math.max(w.y,q.y),t&&(c[z+p]=w.x,c[z+1+p]=w.y),c[z++]=w.x,c[z++]=w.y}(n.normal||n.tangent||n.bitangent||g)&&(b=f.geodeticSurfaceNormal(V,b),g&&(S[m+y]=-b.x,S[u+y]=-b.y,S[T+y]=-b.z),(n.normal||n.tangent||n.bitangent)&&((n.tangent||n.bitangent)&&(M=a.normalize(a.cross(a.UNIT_Z,b,M),M),C.multiplyByVector(U,M,M)),n.normal&&(l[m]=b.x,l[u]=b.y,l[T]=b.z,t&&(l[m+y]=-b.x,l[u+y]=-b.y,l[T+y]=-b.z)),n.tangent&&(A[m]=M.x,A[u]=M.y,A[T]=M.z,t&&(A[m+y]=-M.x,A[u+y]=-M.y,A[T+y]=-M.z)),n.bitangent&&(d=a.normalize(a.cross(b,M,d),d),x[m]=d.x,x[u]=d.y,x[T]=d.z,t&&(x[m+y]=d.x,x[u+y]=d.y,x[T+y]=d.z))))}if(n.st){O=c.length;for(let m=0;m<O;m+=2)c[m]=(c[m]-B.x)/(q.x-B.x),c[m+1]=(c[m+1]-B.y)/(q.y-B.y)}let L=new ht;if(n.position){let m=G.raisePositionsToHeight(o,e,t);L.position=new F({componentDatatype:P.DOUBLE,componentsPerAttribute:3,values:m})}if(n.st&&(L.st=new F({componentDatatype:P.FLOAT,componentsPerAttribute:2,values:c})),n.normal&&(L.normal=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:l})),n.tangent&&(L.tangent=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:A})),n.bitangent&&(L.bitangent=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:x})),g&&(L.extrudeDirection=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:S})),t&&Z(e.offsetAttribute)){let m=new Uint8Array(N);if(e.offsetAttribute===$.TOP)m=m.fill(1,0,N/2);else{let u=e.offsetAttribute===$.NONE?0:1;m=m.fill(u)}L.applyOffset=new F({componentDatatype:P.UNSIGNED_BYTE,componentsPerAttribute:1,values:m})}return L}function jt(o){let e=new Array(12*(o*(o+1))-6),t=0,n,s,i,r,f;for(n=0,i=1,r=0;r<3;r++)e[t++]=i++,e[t++]=n,e[t++]=i;for(r=2;r<o+1;++r){for(i=r*(r+1)-1,n=(r-1)*r-1,e[t++]=i++,e[t++]=n,e[t++]=i,s=2*r,f=0;f<s-1;++f)e[t++]=i,e[t++]=n++,e[t++]=n,e[t++]=i++,e[t++]=n,e[t++]=i;e[t++]=i++,e[t++]=n,e[t++]=i}for(s=o*2,++i,++n,r=0;r<s-1;++r)e[t++]=i,e[t++]=n++,e[t++]=n,e[t++]=i++,e[t++]=n,e[t++]=i;for(e[t++]=i,e[t++]=n++,e[t++]=n,e[t++]=i++,e[t++]=n++,e[t++]=n,++n,r=o-1;r>1;--r){for(e[t++]=n++,e[t++]=n,e[t++]=i,s=2*r,f=0;f<s-1;++f)e[t++]=i,e[t++]=n++,e[t++]=n,e[t++]=i++,e[t++]=n,e[t++]=i;e[t++]=n++,e[t++]=n++,e[t++]=i++}for(r=0;r<3;r++)e[t++]=n++,e[t++]=n,e[t++]=i;return e}var K=new a;function Dt(o){let e=o.center;K=a.multiplyByScalar(o.ellipsoid.geodeticSurfaceNormal(e,K),o.height,K),K=a.add(e,K,K);let t=new it(K,o.semiMajorAxis),n=G.computeEllipsePositions(o,!0,!1),s=n.positions,i=n.numPts,r=Ot(s,o,!1),f=jt(i);return f=st.createTypedArray(s.length/3,f),{boundingSphere:t,attributes:r,indices:f}}function vt(o,e){let t=e.vertexFormat,n=e.center,s=e.semiMajorAxis,i=e.semiMinorAxis,r=e.ellipsoid,f=e.height,h=e.extrudedHeight,N=e.stRotation,g=o.length/3*2,c=new Float64Array(g*3),l=t.st?new Float32Array(g*2):void 0,A=t.normal?new Float32Array(g*3):void 0,x=t.tangent?new Float32Array(g*3):void 0,S=t.bitangent?new Float32Array(g*3):void 0,z=e.shadowVolume,b=z?new Float32Array(g*3):void 0,M=0,d=Tt,_=Nt,I=Et,W=new lt(r),J=W.project(r.cartesianToCartographic(n,ft),Pt),U=r.scaleToGeodeticSurface(n,tt);r.geodeticSurfaceNormal(U,U);let B=nt.fromAxisAngle(U,N,gt),q=C.fromQuaternion(B,Mt),O=Q.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Ft),y=Q.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,St),p=o.length,L=p/3*2;for(let u=0;u<p;u+=3){let T=u+1,V=u+2,E=a.fromArray(o,u,tt),j;if(t.st){let et=C.multiplyByVector(q,E,pt),X=W.project(r.cartesianToCartographic(et,ft),xt);a.subtract(X,J,X),w.x=(X.x+s)/(2*s),w.y=(X.y+i)/(2*i),O.x=Math.min(w.x,O.x),O.y=Math.min(w.y,O.y),y.x=Math.max(w.x,y.x),y.y=Math.max(w.y,y.y),l[M+L]=w.x,l[M+1+L]=w.y,l[M++]=w.x,l[M++]=w.y}E=r.scaleToGeodeticSurface(E,E),j=a.clone(E,pt),d=r.geodeticSurfaceNormal(E,d),z&&(b[u+p]=-d.x,b[T+p]=-d.y,b[V+p]=-d.z);let at=a.multiplyByScalar(d,f,wt);if(E=a.add(E,at,E),at=a.multiplyByScalar(d,h,at),j=a.add(j,at,j),t.position&&(c[u+p]=j.x,c[T+p]=j.y,c[V+p]=j.z,c[u]=E.x,c[T]=E.y,c[V]=E.z),t.normal||t.tangent||t.bitangent){I=a.clone(d,I);let et=a.fromArray(o,(u+3)%p,wt);a.subtract(et,E,et);let X=a.subtract(j,E,xt);d=a.normalize(a.cross(X,et,d),d),t.normal&&(A[u]=d.x,A[T]=d.y,A[V]=d.z,A[u+p]=d.x,A[T+p]=d.y,A[V+p]=d.z),t.tangent&&(_=a.normalize(a.cross(I,d,_),_),x[u]=_.x,x[T]=_.y,x[V]=_.z,x[u+p]=_.x,x[u+1+p]=_.y,x[u+2+p]=_.z),t.bitangent&&(S[u]=I.x,S[T]=I.y,S[V]=I.z,S[u+p]=I.x,S[T+p]=I.y,S[V+p]=I.z)}}if(t.st){p=l.length;for(let u=0;u<p;u+=2)l[u]=(l[u]-O.x)/(y.x-O.x),l[u+1]=(l[u+1]-O.y)/(y.y-O.y)}let m=new ht;if(t.position&&(m.position=new F({componentDatatype:P.DOUBLE,componentsPerAttribute:3,values:c})),t.st&&(m.st=new F({componentDatatype:P.FLOAT,componentsPerAttribute:2,values:l})),t.normal&&(m.normal=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:A})),t.tangent&&(m.tangent=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:x})),t.bitangent&&(m.bitangent=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:S})),z&&(m.extrudeDirection=new F({componentDatatype:P.FLOAT,componentsPerAttribute:3,values:b})),Z(e.offsetAttribute)){let u=new Uint8Array(g);if(e.offsetAttribute===$.TOP)u=u.fill(1,0,g/2);else{let T=e.offsetAttribute===$.NONE?0:1;u=u.fill(T)}m.applyOffset=new F({componentDatatype:P.UNSIGNED_BYTE,componentsPerAttribute:1,values:u})}return m}function zt(o){let e=o.length/3,t=st.createTypedArray(e,e*6),n=0;for(let s=0;s<e;s++){let i=s,r=s+e,f=(i+1)%e,h=f+e;t[n++]=i,t[n++]=r,t[n++]=f,t[n++]=f,t[n++]=r,t[n++]=h}return t}var mt=new it,ut=new it;function Bt(o){let e=o.center,t=o.ellipsoid,n=o.semiMajorAxis,s=a.multiplyByScalar(t.geodeticSurfaceNormal(e,tt),o.height,tt);mt.center=a.add(e,s,mt.center),mt.radius=n,s=a.multiplyByScalar(t.geodeticSurfaceNormal(e,s),o.extrudedHeight,s),ut.center=a.add(e,s,ut.center),ut.radius=n;let i=G.computeEllipsePositions(o,!0,!0),r=i.positions,f=i.numPts,h=i.outerPositions,N=it.union(mt,ut),g=Ot(r,o,!0),c=jt(f),l=c.length;c.length=l*2;let A=r.length/3;for(let _=0;_<l;_+=3)c[_+l]=c[_+2]+A,c[_+1+l]=c[_+1]+A,c[_+2+l]=c[_]+A;let x=st.createTypedArray(A*2/3,c),S=new rt({attributes:g,indices:x,primitiveType:ct.TRIANGLES}),z=vt(h,o);c=zt(h);let b=st.createTypedArray(h.length*2/3,c),M=new rt({attributes:z,indices:b,primitiveType:ct.TRIANGLES}),d=yt.combineInstances([new At({geometry:S}),new At({geometry:M})]);return{boundingSphere:N,attributes:d[0].attributes,indices:d[0].indices}}function Ct(o,e,t,n,s,i,r){let h=G.computeEllipsePositions({center:o,semiMajorAxis:e,semiMinorAxis:t,rotation:n,granularity:s},!1,!0).outerPositions,N=h.length/3,g=new Array(N);for(let l=0;l<N;++l)g[l]=a.fromArray(h,l*3);let c=bt.fromCartesianArray(g,i,r);return c.width>R.PI&&(c.north=c.north>0?R.PI_OVER_TWO-R.EPSILON7:c.north,c.south=c.south<0?R.EPSILON7-R.PI_OVER_TWO:c.south,c.east=R.PI,c.west=-R.PI),c}function Y(o){o=o??dt.EMPTY_OBJECT;let e=o.center,t=o.ellipsoid??D.default,n=o.semiMajorAxis,s=o.semiMinorAxis,i=o.granularity??R.RADIANS_PER_DEGREE,r=o.vertexFormat??v.DEFAULT;if(k.defined("options.center",e),k.typeOf.number("options.semiMajorAxis",n),k.typeOf.number("options.semiMinorAxis",s),n<s)throw new ot("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(i<=0)throw new ot("granularity must be greater than zero.");let f=o.height??0,h=o.extrudedHeight??f;this._center=a.clone(e),this._semiMajorAxis=n,this._semiMinorAxis=s,this._ellipsoid=D.clone(t),this._rotation=o.rotation??0,this._stRotation=o.stRotation??0,this._height=Math.max(h,f),this._granularity=i,this._vertexFormat=v.clone(r),this._extrudedHeight=Math.min(h,f),this._shadowVolume=o.shadowVolume??!1,this._workerName="createEllipseGeometry",this._offsetAttribute=o.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}Y.packedLength=a.packedLength+D.packedLength+v.packedLength+9;Y.pack=function(o,e,t){return k.defined("value",o),k.defined("array",e),t=t??0,a.pack(o._center,e,t),t+=a.packedLength,D.pack(o._ellipsoid,e,t),t+=D.packedLength,v.pack(o._vertexFormat,e,t),t+=v.packedLength,e[t++]=o._semiMajorAxis,e[t++]=o._semiMinorAxis,e[t++]=o._rotation,e[t++]=o._stRotation,e[t++]=o._height,e[t++]=o._granularity,e[t++]=o._extrudedHeight,e[t++]=o._shadowVolume?1:0,e[t]=o._offsetAttribute??-1,e};var Rt=new a,It=new D,Lt=new v,H={center:Rt,ellipsoid:It,vertexFormat:Lt,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};Y.unpack=function(o,e,t){k.defined("array",o),e=e??0;let n=a.unpack(o,e,Rt);e+=a.packedLength;let s=D.unpack(o,e,It);e+=D.packedLength;let i=v.unpack(o,e,Lt);e+=v.packedLength;let r=o[e++],f=o[e++],h=o[e++],N=o[e++],g=o[e++],c=o[e++],l=o[e++],A=o[e++]===1,x=o[e];return Z(t)?(t._center=a.clone(n,t._center),t._ellipsoid=D.clone(s,t._ellipsoid),t._vertexFormat=v.clone(i,t._vertexFormat),t._semiMajorAxis=r,t._semiMinorAxis=f,t._rotation=h,t._stRotation=N,t._height=g,t._granularity=c,t._extrudedHeight=l,t._shadowVolume=A,t._offsetAttribute=x===-1?void 0:x,t):(H.height=g,H.extrudedHeight=l,H.granularity=c,H.stRotation=N,H.rotation=h,H.semiMajorAxis=r,H.semiMinorAxis=f,H.shadowVolume=A,H.offsetAttribute=x===-1?void 0:x,new Y(H))};Y.computeRectangle=function(o,e){o=o??dt.EMPTY_OBJECT;let t=o.center,n=o.ellipsoid??D.default,s=o.semiMajorAxis,i=o.semiMinorAxis,r=o.granularity??R.RADIANS_PER_DEGREE,f=o.rotation??0;if(k.defined("options.center",t),k.typeOf.number("options.semiMajorAxis",s),k.typeOf.number("options.semiMinorAxis",i),s<i)throw new ot("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(r<=0)throw new ot("granularity must be greater than zero.");return Ct(t,s,i,f,r,n,e)};Y.createGeometry=function(o){if(o._semiMajorAxis<=0||o._semiMinorAxis<=0)return;let e=o._height,t=o._extrudedHeight,n=!R.equalsEpsilon(e,t,0,R.EPSILON2);o._center=o._ellipsoid.scaleToGeodeticSurface(o._center,o._center);let s={center:o._center,semiMajorAxis:o._semiMajorAxis,semiMinorAxis:o._semiMinorAxis,ellipsoid:o._ellipsoid,rotation:o._rotation,height:e,granularity:o._granularity,vertexFormat:o._vertexFormat,stRotation:o._stRotation},i;if(n)s.extrudedHeight=t,s.shadowVolume=o._shadowVolume,s.offsetAttribute=o._offsetAttribute,i=Bt(s);else if(i=Dt(s),Z(o._offsetAttribute)){let r=i.attributes.position.values.length,f=o._offsetAttribute===$.NONE?0:1,h=new Uint8Array(r/3).fill(f);i.attributes.applyOffset=new F({componentDatatype:P.UNSIGNED_BYTE,componentsPerAttribute:1,values:h})}return new rt({attributes:i.attributes,indices:i.indices,primitiveType:ct.TRIANGLES,boundingSphere:i.boundingSphere,offsetAttribute:o._offsetAttribute})};Y.createShadowVolume=function(o,e,t){let n=o._granularity,s=o._ellipsoid,i=e(n,s),r=t(n,s);return new Y({center:o._center,semiMajorAxis:o._semiMajorAxis,semiMinorAxis:o._semiMinorAxis,ellipsoid:s,rotation:o._rotation,stRotation:o._stRotation,granularity:n,extrudedHeight:i,height:r,vertexFormat:v.POSITION_ONLY,shadowVolume:!0})};function kt(o){let e=-o._stRotation;if(e===0)return[0,0,0,1,1,0];let n=G.computeEllipsePositions({center:o._center,semiMajorAxis:o._semiMajorAxis,semiMinorAxis:o._semiMinorAxis,rotation:o._rotation,granularity:o._granularity},!1,!0).outerPositions,s=n.length/3,i=new Array(s);for(let h=0;h<s;++h)i[h]=a.fromArray(n,h*3);let r=o._ellipsoid,f=o.rectangle;return rt._textureCoordinateRotationPoints(i,e,r,f)}Object.defineProperties(Y.prototype,{rectangle:{get:function(){return Z(this._rectangle)||(this._rectangle=Ct(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return Z(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=kt(this)),this._textureCoordinateRotationPoints}}});var de=Y;export{de as a};
