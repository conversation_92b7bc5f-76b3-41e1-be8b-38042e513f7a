/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as W}from"./chunk-YZ5FJSO3.js";import{a as U}from"./chunk-X76FJHF4.js";import"./chunk-NYNUONZK.js";import"./chunk-5YCKGPZ3.js";import{a as x,b as I,c as j}from"./chunk-2BFZMXUG.js";import"./chunk-WCO3MWIK.js";import"./chunk-BQNAZUCV.js";import{a as g}from"./chunk-KA7ZX4BQ.js";import"./chunk-C7EXJG2O.js";import"./chunk-LM5DN6BS.js";import{a as $,b as M}from"./chunk-452MW5E6.js";import{a as Z}from"./chunk-IOJRZZG4.js";import"./chunk-QF5X4OGE.js";import"./chunk-S3NLG5WM.js";import"./chunk-AAOMPF7M.js";import{a as X}from"./chunk-PFXLBIMV.js";import{a as Q}from"./chunk-PSBTKXXJ.js";import{b as V,c as Y,d as B}from"./chunk-UJOKCDQH.js";import{d as K}from"./chunk-KYREMICR.js";import"./chunk-VBRVI5XI.js";import{a as N}from"./chunk-5ZFOKSDK.js";import{a as E,c as D,d as f,f as J}from"./chunk-7L2LUDC3.js";import{a as H}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{a as A}from"./chunk-PX3QTMVS.js";import{e as _}from"./chunk-FE4HG5RY.js";function oe(t,e,n,o){let m=new Q;o.position&&(m.position=new B({componentDatatype:N.DOUBLE,componentsPerAttribute:3,values:t}));let i=e.length,u=t.length/3,w=(u-i*2)/(i*2),L=M.triangulate(e),R=(w-1)*i*6+L.length*2,r=X.createTypedArray(u,R),l,s,d,T,b,F,q=i*2,p=0;for(l=0;l<w-1;l++){for(s=0;s<i-1;s++)d=s*2+l*i*2,F=d+q,T=d+1,b=T+q,r[p++]=T,r[p++]=d,r[p++]=b,r[p++]=b,r[p++]=d,r[p++]=F;d=i*2-2+l*i*2,T=d+1,b=T+q,F=d+q,r[p++]=T,r[p++]=d,r[p++]=b,r[p++]=b,r[p++]=d,r[p++]=F}if(o.st||o.tangent||o.bitangent){let c=new Float32Array(u*2),C=1/(w-1),P=1/n.height,O=n.height/2,y,a,h=0;for(l=0;l<w;l++){for(y=l*C,a=P*(e[0].y+O),c[h++]=y,c[h++]=a,s=1;s<i;s++)a=P*(e[s].y+O),c[h++]=y,c[h++]=a,c[h++]=y,c[h++]=a;a=P*(e[0].y+O),c[h++]=y,c[h++]=a}for(s=0;s<i;s++)y=0,a=P*(e[s].y+O),c[h++]=y,c[h++]=a;for(s=0;s<i;s++)y=(w-1)*C,a=P*(e[s].y+O),c[h++]=y,c[h++]=a;m.st=new B({componentDatatype:N.FLOAT,componentsPerAttribute:2,values:new Float32Array(c)})}let G=u-i*2;for(l=0;l<L.length;l+=3){let c=L[l]+G,C=L[l+1]+G,P=L[l+2]+G;r[p++]=c,r[p++]=C,r[p++]=P,r[p++]=P+i,r[p++]=C+i,r[p++]=c+i}let k=new Y({attributes:m,indices:r,boundingSphere:K.fromVertices(t),primitiveType:V.TRIANGLES});if(o.normal&&(k=U.computeNormal(k)),o.tangent||o.bitangent){try{k=U.computeTangentAndBitangent(k)}catch{I("polyline-volume-tangent-bitangent","Unable to compute tangents and bitangents for polyline volume geometry")}o.tangent||(k.attributes.tangent=void 0),o.bitangent||(k.attributes.bitangent=void 0),o.st||(k.attributes.st=void 0)}return k}function v(t){t=t??J.EMPTY_OBJECT;let e=t.polylinePositions,n=t.shapePositions;if(!_(e))throw new A("options.polylinePositions is required.");if(!_(n))throw new A("options.shapePositions is required.");this._positions=e,this._shape=n,this._ellipsoid=f.clone(t.ellipsoid??f.default),this._cornerType=t.cornerType??x.ROUNDED,this._vertexFormat=g.clone(t.vertexFormat??g.DEFAULT),this._granularity=t.granularity??H.RADIANS_PER_DEGREE,this._workerName="createPolylineVolumeGeometry";let o=1+e.length*E.packedLength;o+=1+n.length*D.packedLength,this.packedLength=o+f.packedLength+g.packedLength+2}v.pack=function(t,e,n){if(!_(t))throw new A("value is required");if(!_(e))throw new A("array is required");n=n??0;let o,m=t._positions,i=m.length;for(e[n++]=i,o=0;o<i;++o,n+=E.packedLength)E.pack(m[o],e,n);let u=t._shape;for(i=u.length,e[n++]=i,o=0;o<i;++o,n+=D.packedLength)D.pack(u[o],e,n);return f.pack(t._ellipsoid,e,n),n+=f.packedLength,g.pack(t._vertexFormat,e,n),n+=g.packedLength,e[n++]=t._cornerType,e[n]=t._granularity,e};var ee=f.clone(f.UNIT_SPHERE),te=new g,S={polylinePositions:void 0,shapePositions:void 0,ellipsoid:ee,vertexFormat:te,cornerType:void 0,granularity:void 0};v.unpack=function(t,e,n){if(!_(t))throw new A("array is required");e=e??0;let o,m=t[e++],i=new Array(m);for(o=0;o<m;++o,e+=E.packedLength)i[o]=E.unpack(t,e);m=t[e++];let u=new Array(m);for(o=0;o<m;++o,e+=D.packedLength)u[o]=D.unpack(t,e);let w=f.unpack(t,e,ee);e+=f.packedLength;let L=g.unpack(t,e,te);e+=g.packedLength;let R=t[e++],r=t[e];return _(n)?(n._positions=i,n._shape=u,n._ellipsoid=f.clone(w,n._ellipsoid),n._vertexFormat=g.clone(L,n._vertexFormat),n._cornerType=R,n._granularity=r,n):(S.polylinePositions=i,S.shapePositions=u,S.cornerType=R,S.granularity=r,new v(S))};var ne=new W;v.createGeometry=function(t){let e=t._positions,n=Z(e,E.equalsEpsilon),o=t._shape;if(o=j.removeDuplicatesFromShape(o),n.length<2||o.length<3)return;M.computeWindingOrder2D(o)===$.CLOCKWISE&&o.reverse();let m=W.fromPoints(o,ne),i=j.computePositions(n,o,m,t,!0);return oe(i,o,m,t._vertexFormat)};var z=v;function ie(t,e){return _(e)&&(t=z.unpack(t,e)),t._ellipsoid=f.clone(t._ellipsoid),z.createGeometry(t)}var Re=ie;export{Re as default};
