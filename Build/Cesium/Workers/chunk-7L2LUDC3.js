/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as h}from"./chunk-77KFIUJG.js";import{a as g,b as c}from"./chunk-PX3QTMVS.js";import{e as u}from"./chunk-FE4HG5RY.js";function f(n,e,o){this.x=n??0,this.y=e??0,this.z=o??0}f.fromSpherical=function(n,e){c.typeOf.object("spherical",n),u(e)||(e=new f);let o=n.clock,t=n.cone,i=n.magnitude??1,p=i*Math.sin(t);return e.x=p*Math.cos(o),e.y=p*Math.sin(o),e.z=i*Math.cos(t),e};f.fromElements=function(n,e,o,t){return u(t)?(t.x=n,t.y=e,t.z=o,t):new f(n,e,o)};f.clone=function(n,e){if(u(n))return u(e)?(e.x=n.x,e.y=n.y,e.z=n.z,e):new f(n.x,n.y,n.z)};f.fromCartesian4=f.clone;f.packedLength=3;f.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=o??0,e[o++]=n.x,e[o++]=n.y,e[o]=n.z,e};f.unpack=function(n,e,o){return c.defined("array",n),e=e??0,u(o)||(o=new f),o.x=n[e++],o.y=n[e++],o.z=n[e],o};f.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*3;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new g("If result is a typed array, it must have exactly array.length * 3 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)f.pack(n[i],e,i*3);return e};f.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,3),n.length%3!==0)throw new g("array length must be a multiple of 3.");let o=n.length;u(e)?e.length=o/3:e=new Array(o/3);for(let t=0;t<o;t+=3){let i=t/3;e[i]=f.unpack(n,t,e[i])}return e};f.fromArray=f.unpack;f.maximumComponent=function(n){return c.typeOf.object("cartesian",n),Math.max(n.x,n.y,n.z)};f.minimumComponent=function(n){return c.typeOf.object("cartesian",n),Math.min(n.x,n.y,n.z)};f.minimumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.min(n.x,e.x),o.y=Math.min(n.y,e.y),o.z=Math.min(n.z,e.z),o};f.maximumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.max(n.x,e.x),o.y=Math.max(n.y,e.y),o.z=Math.max(n.z,e.z),o};f.clamp=function(n,e,o,t){c.typeOf.object("value",n),c.typeOf.object("min",e),c.typeOf.object("max",o),c.typeOf.object("result",t);let i=h.clamp(n.x,e.x,o.x),p=h.clamp(n.y,e.y,o.y),d=h.clamp(n.z,e.z,o.z);return t.x=i,t.y=p,t.z=d,t};f.magnitudeSquared=function(n){return c.typeOf.object("cartesian",n),n.x*n.x+n.y*n.y+n.z*n.z};f.magnitude=function(n){return Math.sqrt(f.magnitudeSquared(n))};var X=new f;f.distance=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),f.subtract(n,e,X),f.magnitude(X)};f.distanceSquared=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),f.subtract(n,e,X),f.magnitudeSquared(X)};f.normalize=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=f.magnitude(n);if(e.x=n.x/o,e.y=n.y/o,e.z=n.z/o,isNaN(e.x)||isNaN(e.y)||isNaN(e.z))throw new g("normalized result is not a number");return e};f.dot=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.x+n.y*e.y+n.z*e.z};f.multiplyComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x*e.x,o.y=n.y*e.y,o.z=n.z*e.z,o};f.divideComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x/e.x,o.y=n.y/e.y,o.z=n.z/e.z,o};f.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x+e.x,o.y=n.y+e.y,o.z=n.z+e.z,o};f.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x-e.x,o.y=n.y-e.y,o.z=n.z-e.z,o};f.multiplyByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x*e,o.y=n.y*e,o.z=n.z*e,o};f.divideByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x/e,o.y=n.y/e,o.z=n.z/e,o};f.negate=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=-n.x,e.y=-n.y,e.z=-n.z,e};f.abs=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=Math.abs(n.x),e.y=Math.abs(n.y),e.z=Math.abs(n.z),e};var Tn=new f;f.lerp=function(n,e,o,t){return c.typeOf.object("start",n),c.typeOf.object("end",e),c.typeOf.number("t",o),c.typeOf.object("result",t),f.multiplyByScalar(e,o,Tn),t=f.multiplyByScalar(n,1-o,t),f.add(Tn,t,t)};var Y=new f,dn=new f;f.angleBetween=function(n,e){c.typeOf.object("left",n),c.typeOf.object("right",e),f.normalize(n,Y),f.normalize(e,dn);let o=f.dot(Y,dn),t=f.magnitude(f.cross(Y,dn,Y));return Math.atan2(t,o)};var Yn=new f;f.mostOrthogonalAxis=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=f.normalize(n,Yn);return f.abs(o,o),o.x<=o.y?o.x<=o.z?e=f.clone(f.UNIT_X,e):e=f.clone(f.UNIT_Z,e):o.y<=o.z?e=f.clone(f.UNIT_Y,e):e=f.clone(f.UNIT_Z,e),e};f.projectVector=function(n,e,o){c.defined("a",n),c.defined("b",e),c.defined("result",o);let t=f.dot(n,e)/f.dot(e,e);return f.multiplyByScalar(e,t,o)};f.equals=function(n,e){return n===e||u(n)&&u(e)&&n.x===e.x&&n.y===e.y&&n.z===e.z};f.equalsArray=function(n,e,o){return n.x===e[o]&&n.y===e[o+1]&&n.z===e[o+2]};f.equalsEpsilon=function(n,e,o,t){return n===e||u(n)&&u(e)&&h.equalsEpsilon(n.x,e.x,o,t)&&h.equalsEpsilon(n.y,e.y,o,t)&&h.equalsEpsilon(n.z,e.z,o,t)};f.cross=function(n,e,o){c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o);let t=n.x,i=n.y,p=n.z,d=e.x,m=e.y,s=e.z,b=i*s-p*m,w=p*d-t*s,S=t*m-i*d;return o.x=b,o.y=w,o.z=S,o};f.midpoint=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=(n.x+e.x)*.5,o.y=(n.y+e.y)*.5,o.z=(n.z+e.z)*.5,o};f.fromDegrees=function(n,e,o,t,i){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),n=h.toRadians(n),e=h.toRadians(e),f.fromRadians(n,e,o,t,i)};var T=new f,V=new f;f._ellipsoidRadiiSquared=new f(6378137*6378137,6378137*6378137,6356752314245179e-9*6356752314245179e-9);f.fromRadians=function(n,e,o,t,i){c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),o=o??0;let p=u(t)?t.radiiSquared:f._ellipsoidRadiiSquared,d=Math.cos(e);T.x=d*Math.cos(n),T.y=d*Math.sin(n),T.z=Math.sin(e),T=f.normalize(T,T),f.multiplyComponents(p,T,V);let m=Math.sqrt(f.dot(T,V));return V=f.divideByScalar(V,m,V),T=f.multiplyByScalar(T,o,T),u(i)||(i=new f),f.add(V,T,i)};f.fromDegreesArray=function(n,e,o){if(c.defined("coordinates",n),n.length<2||n.length%2!==0)throw new g("the number of coordinates must be a multiple of 2 and at least 2");let t=n.length;u(o)?o.length=t/2:o=new Array(t/2);for(let i=0;i<t;i+=2){let p=n[i],d=n[i+1],m=i/2;o[m]=f.fromDegrees(p,d,0,e,o[m])}return o};f.fromRadiansArray=function(n,e,o){if(c.defined("coordinates",n),n.length<2||n.length%2!==0)throw new g("the number of coordinates must be a multiple of 2 and at least 2");let t=n.length;u(o)?o.length=t/2:o=new Array(t/2);for(let i=0;i<t;i+=2){let p=n[i],d=n[i+1],m=i/2;o[m]=f.fromRadians(p,d,0,e,o[m])}return o};f.fromDegreesArrayHeights=function(n,e,o){if(c.defined("coordinates",n),n.length<3||n.length%3!==0)throw new g("the number of coordinates must be a multiple of 3 and at least 3");let t=n.length;u(o)?o.length=t/3:o=new Array(t/3);for(let i=0;i<t;i+=3){let p=n[i],d=n[i+1],m=n[i+2],s=i/3;o[s]=f.fromDegrees(p,d,m,e,o[s])}return o};f.fromRadiansArrayHeights=function(n,e,o){if(c.defined("coordinates",n),n.length<3||n.length%3!==0)throw new g("the number of coordinates must be a multiple of 3 and at least 3");let t=n.length;u(o)?o.length=t/3:o=new Array(t/3);for(let i=0;i<t;i+=3){let p=n[i],d=n[i+1],m=n[i+2],s=i/3;o[s]=f.fromRadians(p,d,m,e,o[s])}return o};f.ZERO=Object.freeze(new f(0,0,0));f.ONE=Object.freeze(new f(1,1,1));f.UNIT_X=Object.freeze(new f(1,0,0));f.UNIT_Y=Object.freeze(new f(0,1,0));f.UNIT_Z=Object.freeze(new f(0,0,1));f.prototype.clone=function(n){return f.clone(this,n)};f.prototype.equals=function(n){return f.equals(this,n)};f.prototype.equalsEpsilon=function(n,e,o){return f.equalsEpsilon(this,n,e,o)};f.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z})`};var y=f;var Xn=new y,Gn=new y;function Hn(n,e,o,t,i){if(!u(n))throw new g("cartesian is required.");if(!u(e))throw new g("oneOverRadii is required.");if(!u(o))throw new g("oneOverRadiiSquared is required.");if(!u(t))throw new g("centerToleranceSquared is required.");let p=n.x,d=n.y,m=n.z,s=e.x,b=e.y,w=e.z,S=p*p*s*s,R=d*d*b*b,M=m*m*w*w,C=S+R+M,_=Math.sqrt(1/C),N=y.multiplyByScalar(n,_,Xn);if(C<t)return isFinite(_)?y.clone(N,i):void 0;let B=o.x,U=o.y,P=o.z,F=Gn;F.x=N.x*B*2,F.y=N.y*U*2,F.z=N.z*P*2;let Z=(1-_)*y.magnitude(n)/(.5*y.magnitude(F)),Mn=0,rn,qn,I,L,W,pn,un,an,En,An,_n;do{Z-=Mn,I=1/(1+Z*B),L=1/(1+Z*U),W=1/(1+Z*P),pn=I*I,un=L*L,an=W*W,En=pn*I,An=un*L,_n=an*W,rn=S*pn+R*un+M*an-1,qn=S*En*B+R*An*U+M*_n*P;let Zn=-2*qn;Mn=rn/Zn}while(Math.abs(rn)>h.EPSILON12);return u(i)?(i.x=p*I,i.y=d*L,i.z=m*W,i):new y(p*I,d*L,m*W)}var G=Hn;function j(n,e,o){this.longitude=n??0,this.latitude=e??0,this.height=o??0}j.fromRadians=function(n,e,o,t){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),o=o??0,u(t)?(t.longitude=n,t.latitude=e,t.height=o,t):new j(n,e,o)};j.fromDegrees=function(n,e,o,t){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),n=h.toRadians(n),e=h.toRadians(e),j.fromRadians(n,e,o,t)};var Qn=new y,Jn=new y,Kn=new y;j._ellipsoidOneOverRadii=new y(1/6378137,1/6378137,1/6356752314245179e-9);j._ellipsoidOneOverRadiiSquared=new y(1/(6378137*6378137),1/(6378137*6378137),1/(6356752314245179e-9*6356752314245179e-9));j._ellipsoidCenterToleranceSquared=h.EPSILON1;j.fromCartesian=function(n,e,o){let t=u(e)?e.oneOverRadii:j._ellipsoidOneOverRadii,i=u(e)?e.oneOverRadiiSquared:j._ellipsoidOneOverRadiiSquared,p=u(e)?e._centerToleranceSquared:j._ellipsoidCenterToleranceSquared,d=G(n,t,i,p,Jn);if(!u(d))return;let m=y.multiplyComponents(d,i,Qn);m=y.normalize(m,m);let s=y.subtract(n,d,Kn),b=Math.atan2(m.y,m.x),w=Math.asin(m.z),S=h.sign(y.dot(s,n))*y.magnitude(s);return u(o)?(o.longitude=b,o.latitude=w,o.height=S,o):new j(b,w,S)};j.toCartesian=function(n,e,o){return c.defined("cartographic",n),y.fromRadians(n.longitude,n.latitude,n.height,e,o)};j.clone=function(n,e){if(u(n))return u(e)?(e.longitude=n.longitude,e.latitude=n.latitude,e.height=n.height,e):new j(n.longitude,n.latitude,n.height)};j.equals=function(n,e){return n===e||u(n)&&u(e)&&n.longitude===e.longitude&&n.latitude===e.latitude&&n.height===e.height};j.equalsEpsilon=function(n,e,o){return o=o??0,n===e||u(n)&&u(e)&&Math.abs(n.longitude-e.longitude)<=o&&Math.abs(n.latitude-e.latitude)<=o&&Math.abs(n.height-e.height)<=o};j.ZERO=Object.freeze(new j(0,0,0));j.prototype.clone=function(n){return j.clone(this,n)};j.prototype.equals=function(n){return j.equals(this,n)};j.prototype.equalsEpsilon=function(n,e){return j.equalsEpsilon(this,n,e)};j.prototype.toString=function(){return`(${this.longitude}, ${this.latitude}, ${this.height})`};var $=j;function a(n,e){this.x=n??0,this.y=e??0}a.fromElements=function(n,e,o){return u(o)?(o.x=n,o.y=e,o):new a(n,e)};a.clone=function(n,e){if(u(n))return u(e)?(e.x=n.x,e.y=n.y,e):new a(n.x,n.y)};a.fromCartesian3=a.clone;a.fromCartesian4=a.clone;a.packedLength=2;a.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=o??0,e[o++]=n.x,e[o]=n.y,e};a.unpack=function(n,e,o){return c.defined("array",n),e=e??0,u(o)||(o=new a),o.x=n[e++],o.y=n[e],o};a.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*2;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new g("If result is a typed array, it must have exactly array.length * 2 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)a.pack(n[i],e,i*2);return e};a.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,2),n.length%2!==0)throw new g("array length must be a multiple of 2.");let o=n.length;u(e)?e.length=o/2:e=new Array(o/2);for(let t=0;t<o;t+=2){let i=t/2;e[i]=a.unpack(n,t,e[i])}return e};a.fromArray=a.unpack;a.maximumComponent=function(n){return c.typeOf.object("cartesian",n),Math.max(n.x,n.y)};a.minimumComponent=function(n){return c.typeOf.object("cartesian",n),Math.min(n.x,n.y)};a.minimumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.min(n.x,e.x),o.y=Math.min(n.y,e.y),o};a.maximumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.max(n.x,e.x),o.y=Math.max(n.y,e.y),o};a.clamp=function(n,e,o,t){c.typeOf.object("value",n),c.typeOf.object("min",e),c.typeOf.object("max",o),c.typeOf.object("result",t);let i=h.clamp(n.x,e.x,o.x),p=h.clamp(n.y,e.y,o.y);return t.x=i,t.y=p,t};a.magnitudeSquared=function(n){return c.typeOf.object("cartesian",n),n.x*n.x+n.y*n.y};a.magnitude=function(n){return Math.sqrt(a.magnitudeSquared(n))};var H=new a;a.distance=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.subtract(n,e,H),a.magnitude(H)};a.distanceSquared=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.subtract(n,e,H),a.magnitudeSquared(H)};a.normalize=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=a.magnitude(n);if(e.x=n.x/o,e.y=n.y/o,isNaN(e.x)||isNaN(e.y))throw new g("normalized result is not a number");return e};a.dot=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.x+n.y*e.y};a.cross=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.y-n.y*e.x};a.multiplyComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x*e.x,o.y=n.y*e.y,o};a.divideComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x/e.x,o.y=n.y/e.y,o};a.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x+e.x,o.y=n.y+e.y,o};a.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x-e.x,o.y=n.y-e.y,o};a.multiplyByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x*e,o.y=n.y*e,o};a.divideByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x/e,o.y=n.y/e,o};a.negate=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=-n.x,e.y=-n.y,e};a.abs=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=Math.abs(n.x),e.y=Math.abs(n.y),e};var ln=new a;a.lerp=function(n,e,o,t){return c.typeOf.object("start",n),c.typeOf.object("end",e),c.typeOf.number("t",o),c.typeOf.object("result",t),a.multiplyByScalar(e,o,ln),t=a.multiplyByScalar(n,1-o,t),a.add(ln,t,t)};var Cn=new a,Nn=new a;a.angleBetween=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.normalize(n,Cn),a.normalize(e,Nn),h.acosClamped(a.dot(Cn,Nn))};var ne=new a;a.mostOrthogonalAxis=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=a.normalize(n,ne);return a.abs(o,o),o.x<=o.y?e=a.clone(a.UNIT_X,e):e=a.clone(a.UNIT_Y,e),e};a.equals=function(n,e){return n===e||u(n)&&u(e)&&n.x===e.x&&n.y===e.y};a.equalsArray=function(n,e,o){return n.x===e[o]&&n.y===e[o+1]};a.equalsEpsilon=function(n,e,o,t){return n===e||u(n)&&u(e)&&h.equalsEpsilon(n.x,e.x,o,t)&&h.equalsEpsilon(n.y,e.y,o,t)};a.ZERO=Object.freeze(new a(0,0));a.ONE=Object.freeze(new a(1,1));a.UNIT_X=Object.freeze(new a(1,0));a.UNIT_Y=Object.freeze(new a(0,1));a.prototype.clone=function(n){return a.clone(this,n)};a.prototype.equals=function(n){return a.equals(this,n)};a.prototype.equalsEpsilon=function(n,e,o){return a.equalsEpsilon(this,n,e,o)};a.prototype.toString=function(){return`(${this.x}, ${this.y})`};var yn=a;function Fn(n,e,o,t){e=e??0,o=o??0,t=t??0,c.typeOf.number.greaterThanOrEquals("x",e,0),c.typeOf.number.greaterThanOrEquals("y",o,0),c.typeOf.number.greaterThanOrEquals("z",t,0),n._radii=new y(e,o,t),n._radiiSquared=new y(e*e,o*o,t*t),n._radiiToTheFourth=new y(e*e*e*e,o*o*o*o,t*t*t*t),n._oneOverRadii=new y(e===0?0:1/e,o===0?0:1/o,t===0?0:1/t),n._oneOverRadiiSquared=new y(e===0?0:1/(e*e),o===0?0:1/(o*o),t===0?0:1/(t*t)),n._minimumRadius=Math.min(e,o,t),n._maximumRadius=Math.max(e,o,t),n._centerToleranceSquared=h.EPSILON1,n._radiiSquared.z!==0&&(n._squaredXOverSquaredZ=n._radiiSquared.x/n._radiiSquared.z)}function O(n,e,o){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,Fn(this,n,e,o)}Object.defineProperties(O.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}});O.clone=function(n,e){if(!u(n))return;let o=n._radii;return u(e)?(y.clone(o,e._radii),y.clone(n._radiiSquared,e._radiiSquared),y.clone(n._radiiToTheFourth,e._radiiToTheFourth),y.clone(n._oneOverRadii,e._oneOverRadii),y.clone(n._oneOverRadiiSquared,e._oneOverRadiiSquared),e._minimumRadius=n._minimumRadius,e._maximumRadius=n._maximumRadius,e._centerToleranceSquared=n._centerToleranceSquared,e):new O(o.x,o.y,o.z)};O.fromCartesian3=function(n,e){return u(e)||(e=new O),u(n)&&Fn(e,n.x,n.y,n.z),e};O.WGS84=Object.freeze(new O(6378137,6378137,6356752314245179e-9));O.UNIT_SPHERE=Object.freeze(new O(1,1,1));O.MOON=Object.freeze(new O(h.LUNAR_RADIUS,h.LUNAR_RADIUS,h.LUNAR_RADIUS));O._default=O.WGS84;Object.defineProperties(O,{default:{get:function(){return O._default},set:function(n){c.typeOf.object("value",n),O._default=n,y._ellipsoidRadiiSquared=n.radiiSquared,$._ellipsoidOneOverRadii=n.oneOverRadii,$._ellipsoidOneOverRadiiSquared=n.oneOverRadiiSquared,$._ellipsoidCenterToleranceSquared=n._centerToleranceSquared}}});O.prototype.clone=function(n){return O.clone(this,n)};O.packedLength=y.packedLength;O.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=o??0,y.pack(n._radii,e,o),e};O.unpack=function(n,e,o){c.defined("array",n),e=e??0;let t=y.unpack(n,e);return O.fromCartesian3(t,o)};O.prototype.geocentricSurfaceNormal=y.normalize;O.prototype.geodeticSurfaceNormalCartographic=function(n,e){c.typeOf.object("cartographic",n);let o=n.longitude,t=n.latitude,i=Math.cos(t),p=i*Math.cos(o),d=i*Math.sin(o),m=Math.sin(t);return u(e)||(e=new y),e.x=p,e.y=d,e.z=m,y.normalize(e,e)};O.prototype.geodeticSurfaceNormal=function(n,e){if(c.typeOf.object("cartesian",n),isNaN(n.x)||isNaN(n.y)||isNaN(n.z))throw new g("cartesian has a NaN component");if(!y.equalsEpsilon(n,y.ZERO,h.EPSILON14))return u(e)||(e=new y),e=y.multiplyComponents(n,this._oneOverRadiiSquared,e),y.normalize(e,e)};var ee=new y,oe=new y;O.prototype.cartographicToCartesian=function(n,e){let o=ee,t=oe;this.geodeticSurfaceNormalCartographic(n,o),y.multiplyComponents(this._radiiSquared,o,t);let i=Math.sqrt(y.dot(o,t));return y.divideByScalar(t,i,t),y.multiplyByScalar(o,n.height,o),u(e)||(e=new y),y.add(t,o,e)};O.prototype.cartographicArrayToCartesianArray=function(n,e){c.defined("cartographics",n);let o=n.length;u(e)?e.length=o:e=new Array(o);for(let t=0;t<o;t++)e[t]=this.cartographicToCartesian(n[t],e[t]);return e};var te=new y,ce=new y,ie=new y;O.prototype.cartesianToCartographic=function(n,e){let o=this.scaleToGeodeticSurface(n,ce);if(!u(o))return;let t=this.geodeticSurfaceNormal(o,te),i=y.subtract(n,o,ie),p=Math.atan2(t.y,t.x),d=Math.asin(t.z),m=h.sign(y.dot(i,n))*y.magnitude(i);return u(e)?(e.longitude=p,e.latitude=d,e.height=m,e):new $(p,d,m)};O.prototype.cartesianArrayToCartographicArray=function(n,e){c.defined("cartesians",n);let o=n.length;u(e)?e.length=o:e=new Array(o);for(let t=0;t<o;++t)e[t]=this.cartesianToCartographic(n[t],e[t]);return e};O.prototype.scaleToGeodeticSurface=function(n,e){return G(n,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,e)};O.prototype.scaleToGeocentricSurface=function(n,e){c.typeOf.object("cartesian",n),u(e)||(e=new y);let o=n.x,t=n.y,i=n.z,p=this._oneOverRadiiSquared,d=1/Math.sqrt(o*o*p.x+t*t*p.y+i*i*p.z);return y.multiplyByScalar(n,d,e)};O.prototype.transformPositionToScaledSpace=function(n,e){return u(e)||(e=new y),y.multiplyComponents(n,this._oneOverRadii,e)};O.prototype.transformPositionFromScaledSpace=function(n,e){return u(e)||(e=new y),y.multiplyComponents(n,this._radii,e)};O.prototype.equals=function(n){return this===n||u(n)&&y.equals(this._radii,n._radii)};O.prototype.toString=function(){return this._radii.toString()};O.prototype.getSurfaceNormalIntersectionWithZAxis=function(n,e,o){if(c.typeOf.object("position",n),!h.equalsEpsilon(this._radii.x,this._radii.y,h.EPSILON15))throw new g("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");c.typeOf.number.greaterThan("Ellipsoid.radii.z",this._radii.z,0),e=e??0;let t=this._squaredXOverSquaredZ;if(u(o)||(o=new y),o.x=0,o.y=0,o.z=n.z*(1-t),!(Math.abs(o.z)>=this._radii.z-e))return o};var fe=new y;O.prototype.getLocalCurvature=function(n,e){c.typeOf.object("surfacePosition",n),u(e)||(e=new yn);let o=this.getSurfaceNormalIntersectionWithZAxis(n,0,fe),t=y.distance(n,o),i=this.minimumRadius*t/this.maximumRadius**2,p=t*i**2;return yn.fromElements(1/t,1/p,e)};var re=[.14887433898163,.43339539412925,.67940956829902,.86506336668898,.97390652851717,0],pe=[.29552422471475,.26926671930999,.21908636251598,.14945134915058,.066671344308684,0];function xn(n,e,o){c.typeOf.number("a",n),c.typeOf.number("b",e),c.typeOf.func("func",o);let t=.5*(e+n),i=.5*(e-n),p=0;for(let d=0;d<5;d++){let m=i*re[d];p+=pe[d]*(o(t+m)+o(t-m))}return p*=i,p}O.prototype.surfaceArea=function(n){c.typeOf.object("rectangle",n);let e=n.west,o=n.east,t=n.south,i=n.north;for(;o<e;)o+=h.TWO_PI;let p=this._radiiSquared,d=p.x,m=p.y,s=p.z,b=d*m;return xn(t,i,function(w){let S=Math.cos(w),R=Math.sin(w);return Math.cos(w)*xn(e,o,function(M){let C=Math.cos(M),_=Math.sin(M);return Math.sqrt(b*R*R+s*(m*C*C+d*_*_)*S*S)})})};var oo=O;function r(n,e,o,t,i,p,d,m,s){this[0]=n??0,this[1]=t??0,this[2]=d??0,this[3]=e??0,this[4]=i??0,this[5]=m??0,this[6]=o??0,this[7]=p??0,this[8]=s??0}r.packedLength=9;r.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=o??0,e[o++]=n[0],e[o++]=n[1],e[o++]=n[2],e[o++]=n[3],e[o++]=n[4],e[o++]=n[5],e[o++]=n[6],e[o++]=n[7],e[o++]=n[8],e};r.unpack=function(n,e,o){return c.defined("array",n),e=e??0,u(o)||(o=new r),o[0]=n[e++],o[1]=n[e++],o[2]=n[e++],o[3]=n[e++],o[4]=n[e++],o[5]=n[e++],o[6]=n[e++],o[7]=n[e++],o[8]=n[e++],o};r.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*9;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new g("If result is a typed array, it must have exactly array.length * 9 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)r.pack(n[i],e,i*9);return e};r.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,9),n.length%9!==0)throw new g("array length must be a multiple of 9.");let o=n.length;u(e)?e.length=o/9:e=new Array(o/9);for(let t=0;t<o;t+=9){let i=t/9;e[i]=r.unpack(n,t,e[i])}return e};r.clone=function(n,e){if(u(n))return u(e)?(e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e):new r(n[0],n[3],n[6],n[1],n[4],n[7],n[2],n[5],n[8])};r.fromArray=r.unpack;r.fromColumnMajorArray=function(n,e){return c.defined("values",n),r.clone(n,e)};r.fromRowMajorArray=function(n,e){return c.defined("values",n),u(e)?(e[0]=n[0],e[1]=n[3],e[2]=n[6],e[3]=n[1],e[4]=n[4],e[5]=n[7],e[6]=n[2],e[7]=n[5],e[8]=n[8],e):new r(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8])};r.fromQuaternion=function(n,e){c.typeOf.object("quaternion",n);let o=n.x*n.x,t=n.x*n.y,i=n.x*n.z,p=n.x*n.w,d=n.y*n.y,m=n.y*n.z,s=n.y*n.w,b=n.z*n.z,w=n.z*n.w,S=n.w*n.w,R=o-d-b+S,M=2*(t-w),C=2*(i+s),_=2*(t+w),N=-o+d-b+S,B=2*(m-p),U=2*(i-s),P=2*(m+p),F=-o-d+b+S;return u(e)?(e[0]=R,e[1]=_,e[2]=U,e[3]=M,e[4]=N,e[5]=P,e[6]=C,e[7]=B,e[8]=F,e):new r(R,M,C,_,N,B,U,P,F)};r.fromHeadingPitchRoll=function(n,e){c.typeOf.object("headingPitchRoll",n);let o=Math.cos(-n.pitch),t=Math.cos(-n.heading),i=Math.cos(n.roll),p=Math.sin(-n.pitch),d=Math.sin(-n.heading),m=Math.sin(n.roll),s=o*t,b=-i*d+m*p*t,w=m*d+i*p*t,S=o*d,R=i*t+m*p*d,M=-m*t+i*p*d,C=-p,_=m*o,N=i*o;return u(e)?(e[0]=s,e[1]=S,e[2]=C,e[3]=b,e[4]=R,e[5]=_,e[6]=w,e[7]=M,e[8]=N,e):new r(s,b,w,S,R,M,C,_,N)};r.fromScale=function(n,e){return c.typeOf.object("scale",n),u(e)?(e[0]=n.x,e[1]=0,e[2]=0,e[3]=0,e[4]=n.y,e[5]=0,e[6]=0,e[7]=0,e[8]=n.z,e):new r(n.x,0,0,0,n.y,0,0,0,n.z)};r.fromUniformScale=function(n,e){return c.typeOf.number("scale",n),u(e)?(e[0]=n,e[1]=0,e[2]=0,e[3]=0,e[4]=n,e[5]=0,e[6]=0,e[7]=0,e[8]=n,e):new r(n,0,0,0,n,0,0,0,n)};r.fromCrossProduct=function(n,e){return c.typeOf.object("vector",n),u(e)?(e[0]=0,e[1]=n.z,e[2]=-n.y,e[3]=-n.z,e[4]=0,e[5]=n.x,e[6]=n.y,e[7]=-n.x,e[8]=0,e):new r(0,-n.z,n.y,n.z,0,-n.x,-n.y,n.x,0)};r.fromRotationX=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=o,e[5]=t,e[6]=0,e[7]=-t,e[8]=o,e):new r(1,0,0,0,o,-t,0,t,o)};r.fromRotationY=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=o,e[1]=0,e[2]=-t,e[3]=0,e[4]=1,e[5]=0,e[6]=t,e[7]=0,e[8]=o,e):new r(o,0,t,0,1,0,-t,0,o)};r.fromRotationZ=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=o,e[1]=t,e[2]=0,e[3]=-t,e[4]=o,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e):new r(o,-t,0,t,o,0,0,0,1)};r.toArray=function(n,e){return c.typeOf.object("matrix",n),u(e)?(e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e):[n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8]]};r.getElementIndex=function(n,e){return c.typeOf.number.greaterThanOrEquals("row",e,0),c.typeOf.number.lessThanOrEquals("row",e,2),c.typeOf.number.greaterThanOrEquals("column",n,0),c.typeOf.number.lessThanOrEquals("column",n,2),n*3+e};r.getColumn=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("result",o);let t=e*3,i=n[t],p=n[t+1],d=n[t+2];return o.x=i,o.y=p,o.z=d,o};r.setColumn=function(n,e,o,t){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("cartesian",o),c.typeOf.object("result",t),t=r.clone(n,t);let i=e*3;return t[i]=o.x,t[i+1]=o.y,t[i+2]=o.z,t};r.getRow=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("result",o);let t=n[e],i=n[e+3],p=n[e+6];return o.x=t,o.y=i,o.z=p,o};r.setRow=function(n,e,o,t){return c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("cartesian",o),c.typeOf.object("result",t),t=r.clone(n,t),t[e]=o.x,t[e+3]=o.y,t[e+6]=o.z,t};var ue=new y;r.setScale=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("scale",e),c.typeOf.object("result",o);let t=r.getScale(n,ue),i=e.x/t.x,p=e.y/t.y,d=e.z/t.z;return o[0]=n[0]*i,o[1]=n[1]*i,o[2]=n[2]*i,o[3]=n[3]*p,o[4]=n[4]*p,o[5]=n[5]*p,o[6]=n[6]*d,o[7]=n[7]*d,o[8]=n[8]*d,o};var ae=new y;r.setUniformScale=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number("scale",e),c.typeOf.object("result",o);let t=r.getScale(n,ae),i=e/t.x,p=e/t.y,d=e/t.z;return o[0]=n[0]*i,o[1]=n[1]*i,o[2]=n[2]*i,o[3]=n[3]*p,o[4]=n[4]*p,o[5]=n[5]*p,o[6]=n[6]*d,o[7]=n[7]*d,o[8]=n[8]*d,o};var mn=new y;r.getScale=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e.x=y.magnitude(y.fromElements(n[0],n[1],n[2],mn)),e.y=y.magnitude(y.fromElements(n[3],n[4],n[5],mn)),e.z=y.magnitude(y.fromElements(n[6],n[7],n[8],mn)),e};var kn=new y;r.getMaximumScale=function(n){return r.getScale(n,kn),y.maximumComponent(kn)};var de=new y;r.setRotation=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("result",o);let t=r.getScale(n,de);return o[0]=e[0]*t.x,o[1]=e[1]*t.x,o[2]=e[2]*t.x,o[3]=e[3]*t.y,o[4]=e[4]*t.y,o[5]=e[5]*t.y,o[6]=e[6]*t.z,o[7]=e[7]*t.z,o[8]=e[8]*t.z,o};var ye=new y;r.getRotation=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=r.getScale(n,ye);return e[0]=n[0]/o.x,e[1]=n[1]/o.x,e[2]=n[2]/o.x,e[3]=n[3]/o.y,e[4]=n[4]/o.y,e[5]=n[5]/o.y,e[6]=n[6]/o.z,e[7]=n[7]/o.z,e[8]=n[8]/o.z,e};r.multiply=function(n,e,o){c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o);let t=n[0]*e[0]+n[3]*e[1]+n[6]*e[2],i=n[1]*e[0]+n[4]*e[1]+n[7]*e[2],p=n[2]*e[0]+n[5]*e[1]+n[8]*e[2],d=n[0]*e[3]+n[3]*e[4]+n[6]*e[5],m=n[1]*e[3]+n[4]*e[4]+n[7]*e[5],s=n[2]*e[3]+n[5]*e[4]+n[8]*e[5],b=n[0]*e[6]+n[3]*e[7]+n[6]*e[8],w=n[1]*e[6]+n[4]*e[7]+n[7]*e[8],S=n[2]*e[6]+n[5]*e[7]+n[8]*e[8];return o[0]=t,o[1]=i,o[2]=p,o[3]=d,o[4]=m,o[5]=s,o[6]=b,o[7]=w,o[8]=S,o};r.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o[0]=n[0]+e[0],o[1]=n[1]+e[1],o[2]=n[2]+e[2],o[3]=n[3]+e[3],o[4]=n[4]+e[4],o[5]=n[5]+e[5],o[6]=n[6]+e[6],o[7]=n[7]+e[7],o[8]=n[8]+e[8],o};r.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o[0]=n[0]-e[0],o[1]=n[1]-e[1],o[2]=n[2]-e[2],o[3]=n[3]-e[3],o[4]=n[4]-e[4],o[5]=n[5]-e[5],o[6]=n[6]-e[6],o[7]=n[7]-e[7],o[8]=n[8]-e[8],o};r.multiplyByVector=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("cartesian",e),c.typeOf.object("result",o);let t=e.x,i=e.y,p=e.z,d=n[0]*t+n[3]*i+n[6]*p,m=n[1]*t+n[4]*i+n[7]*p,s=n[2]*t+n[5]*i+n[8]*p;return o.x=d,o.y=m,o.z=s,o};r.multiplyByScalar=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o[0]=n[0]*e,o[1]=n[1]*e,o[2]=n[2]*e,o[3]=n[3]*e,o[4]=n[4]*e,o[5]=n[5]*e,o[6]=n[6]*e,o[7]=n[7]*e,o[8]=n[8]*e,o};r.multiplyByScale=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.object("scale",e),c.typeOf.object("result",o),o[0]=n[0]*e.x,o[1]=n[1]*e.x,o[2]=n[2]*e.x,o[3]=n[3]*e.y,o[4]=n[4]*e.y,o[5]=n[5]*e.y,o[6]=n[6]*e.z,o[7]=n[7]*e.z,o[8]=n[8]*e.z,o};r.multiplyByUniformScale=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.number("scale",e),c.typeOf.object("result",o),o[0]=n[0]*e,o[1]=n[1]*e,o[2]=n[2]*e,o[3]=n[3]*e,o[4]=n[4]*e,o[5]=n[5]*e,o[6]=n[6]*e,o[7]=n[7]*e,o[8]=n[8]*e,o};r.negate=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e[0]=-n[0],e[1]=-n[1],e[2]=-n[2],e[3]=-n[3],e[4]=-n[4],e[5]=-n[5],e[6]=-n[6],e[7]=-n[7],e[8]=-n[8],e};r.transpose=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=n[0],t=n[3],i=n[6],p=n[1],d=n[4],m=n[7],s=n[2],b=n[5],w=n[8];return e[0]=o,e[1]=t,e[2]=i,e[3]=p,e[4]=d,e[5]=m,e[6]=s,e[7]=b,e[8]=w,e};function me(n){let e=0;for(let o=0;o<9;++o){let t=n[o];e+=t*t}return Math.sqrt(e)}var sn=[1,0,0],On=[2,2,1];function se(n){let e=0;for(let o=0;o<3;++o){let t=n[r.getElementIndex(On[o],sn[o])];e+=2*t*t}return Math.sqrt(e)}function Oe(n,e){let o=h.EPSILON15,t=0,i=1;for(let b=0;b<3;++b){let w=Math.abs(n[r.getElementIndex(On[b],sn[b])]);w>t&&(i=b,t=w)}let p=1,d=0,m=sn[i],s=On[i];if(Math.abs(n[r.getElementIndex(s,m)])>o){let b=n[r.getElementIndex(s,s)],w=n[r.getElementIndex(m,m)],S=n[r.getElementIndex(s,m)],R=(b-w)/2/S,M;R<0?M=-1/(-R+Math.sqrt(1+R*R)):M=1/(R+Math.sqrt(1+R*R)),p=1/Math.sqrt(1+M*M),d=M*p}return e=r.clone(r.IDENTITY,e),e[r.getElementIndex(m,m)]=e[r.getElementIndex(s,s)]=p,e[r.getElementIndex(s,m)]=d,e[r.getElementIndex(m,s)]=-d,e}var Q=new r,Bn=new r;r.computeEigenDecomposition=function(n,e){c.typeOf.object("matrix",n);let o=h.EPSILON20,t=10,i=0,p=0;u(e)||(e={});let d=e.unitary=r.clone(r.IDENTITY,e.unitary),m=e.diagonal=r.clone(n,e.diagonal),s=o*me(m);for(;p<t&&se(m)>s;)Oe(m,Q),r.transpose(Q,Bn),r.multiply(m,Q,m),r.multiply(Bn,m,m),r.multiply(d,Q,d),++i>2&&(++p,i=0);return e};r.abs=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e[0]=Math.abs(n[0]),e[1]=Math.abs(n[1]),e[2]=Math.abs(n[2]),e[3]=Math.abs(n[3]),e[4]=Math.abs(n[4]),e[5]=Math.abs(n[5]),e[6]=Math.abs(n[6]),e[7]=Math.abs(n[7]),e[8]=Math.abs(n[8]),e};r.determinant=function(n){c.typeOf.object("matrix",n);let e=n[0],o=n[3],t=n[6],i=n[1],p=n[4],d=n[7],m=n[2],s=n[5],b=n[8];return e*(p*b-s*d)+i*(s*t-o*b)+m*(o*d-p*t)};r.inverse=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=n[0],t=n[1],i=n[2],p=n[3],d=n[4],m=n[5],s=n[6],b=n[7],w=n[8],S=r.determinant(n);if(Math.abs(S)<=h.EPSILON15)throw new g("matrix is not invertible");e[0]=d*w-b*m,e[1]=b*i-t*w,e[2]=t*m-d*i,e[3]=s*m-p*w,e[4]=o*w-s*i,e[5]=p*i-o*m,e[6]=p*b-s*d,e[7]=s*t-o*b,e[8]=o*d-p*t;let R=1/S;return r.multiplyByScalar(e,R,e)};var be=new r;r.inverseTranspose=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),r.inverse(r.transpose(n,be),e)};r.equals=function(n,e){return n===e||u(n)&&u(e)&&n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]&&n[6]===e[6]&&n[7]===e[7]&&n[8]===e[8]};r.equalsEpsilon=function(n,e,o){return o=o??0,n===e||u(n)&&u(e)&&Math.abs(n[0]-e[0])<=o&&Math.abs(n[1]-e[1])<=o&&Math.abs(n[2]-e[2])<=o&&Math.abs(n[3]-e[3])<=o&&Math.abs(n[4]-e[4])<=o&&Math.abs(n[5]-e[5])<=o&&Math.abs(n[6]-e[6])<=o&&Math.abs(n[7]-e[7])<=o&&Math.abs(n[8]-e[8])<=o};r.IDENTITY=Object.freeze(new r(1,0,0,0,1,0,0,0,1));r.ZERO=Object.freeze(new r(0,0,0,0,0,0,0,0,0));r.COLUMN0ROW0=0;r.COLUMN0ROW1=1;r.COLUMN0ROW2=2;r.COLUMN1ROW0=3;r.COLUMN1ROW1=4;r.COLUMN1ROW2=5;r.COLUMN2ROW0=6;r.COLUMN2ROW1=7;r.COLUMN2ROW2=8;Object.defineProperties(r.prototype,{length:{get:function(){return r.packedLength}}});r.prototype.clone=function(n){return r.clone(this,n)};r.prototype.equals=function(n){return r.equals(this,n)};r.equalsArray=function(n,e,o){return n[0]===e[o]&&n[1]===e[o+1]&&n[2]===e[o+2]&&n[3]===e[o+3]&&n[4]===e[o+4]&&n[5]===e[o+5]&&n[6]===e[o+6]&&n[7]===e[o+7]&&n[8]===e[o+8]};r.prototype.equalsEpsilon=function(n,e){return r.equalsEpsilon(this,n,e)};r.prototype.toString=function(){return`(${this[0]}, ${this[3]}, ${this[6]})
(${this[1]}, ${this[4]}, ${this[7]})
(${this[2]}, ${this[5]}, ${this[8]})`};var uo=r;var bn={};bn.EMPTY_OBJECT=Object.freeze({});bn.EMPTY_ARRAY=Object.freeze([]);var yo=bn;var x,z={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},A={};Object.defineProperties(A,{element:{get:function(){if(A.supportsFullscreen())return document[z.fullscreenElement]}},changeEventName:{get:function(){if(A.supportsFullscreen())return z.fullscreenchange}},errorEventName:{get:function(){if(A.supportsFullscreen())return z.fullscreenerror}},enabled:{get:function(){if(A.supportsFullscreen())return document[z.fullscreenEnabled]}},fullscreen:{get:function(){if(A.supportsFullscreen())return A.element!==null}}});A.supportsFullscreen=function(){if(u(x))return x;x=!1;let n=document.body;if(typeof n.requestFullscreen=="function")return z.requestFullscreen="requestFullscreen",z.exitFullscreen="exitFullscreen",z.fullscreenEnabled="fullscreenEnabled",z.fullscreenElement="fullscreenElement",z.fullscreenchange="fullscreenchange",z.fullscreenerror="fullscreenerror",x=!0,x;let e=["webkit","moz","o","ms","khtml"],o;for(let t=0,i=e.length;t<i;++t){let p=e[t];o=`${p}RequestFullscreen`,typeof n[o]=="function"?(z.requestFullscreen=o,x=!0):(o=`${p}RequestFullScreen`,typeof n[o]=="function"&&(z.requestFullscreen=o,x=!0)),o=`${p}ExitFullscreen`,typeof document[o]=="function"?z.exitFullscreen=o:(o=`${p}CancelFullScreen`,typeof document[o]=="function"&&(z.exitFullscreen=o)),o=`${p}FullscreenEnabled`,document[o]!==void 0?z.fullscreenEnabled=o:(o=`${p}FullScreenEnabled`,document[o]!==void 0&&(z.fullscreenEnabled=o)),o=`${p}FullscreenElement`,document[o]!==void 0?z.fullscreenElement=o:(o=`${p}FullScreenElement`,document[o]!==void 0&&(z.fullscreenElement=o)),o=`${p}fullscreenchange`,document[`on${o}`]!==void 0&&(p==="ms"&&(o="MSFullscreenChange"),z.fullscreenchange=o),o=`${p}fullscreenerror`,document[`on${o}`]!==void 0&&(p==="ms"&&(o="MSFullscreenError"),z.fullscreenerror=o)}return x};A.requestFullscreen=function(n,e){A.supportsFullscreen()&&n[z.requestFullscreen]({vrDisplay:e})};A.exitFullscreen=function(){A.supportsFullscreen()&&document[z.exitFullscreen]()};A._names=z;var Un=A;var E;typeof navigator<"u"?E=navigator:E={};function k(n){let e=n.split(".");for(let o=0,t=e.length;o<t;++o)e[o]=parseInt(e[o],10);return e}var J,Pn;function zn(){if(!u(J)&&(J=!1,!cn())){let n=/ Chrome\/([\.0-9]+)/.exec(E.userAgent);n!==null&&(J=!0,Pn=k(n[1]))}return J}function he(){return zn()&&Pn}var K,In;function Ln(){if(!u(K)&&(K=!1,!zn()&&!cn()&&/ Safari\/[\.0-9]+/.test(E.userAgent))){let n=/ Version\/([\.0-9]+)/.exec(E.userAgent);n!==null&&(K=!0,In=k(n[1]))}return K}function we(){return Ln()&&In}var nn,gn;function Wn(){if(!u(nn)){nn=!1;let n=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(E.userAgent);n!==null&&(nn=!0,gn=k(n[1]),gn.isNightly=!!n[2])}return nn}function je(){return Wn()&&gn}var D,Sn;function Vn(){if(!u(D)){D=!1;let n;E.appName==="Microsoft Internet Explorer"?(n=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(E.userAgent),n!==null&&(D=!0,Sn=k(n[1]))):E.appName==="Netscape"&&(n=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(E.userAgent),n!==null&&(D=!0,Sn=k(n[1])))}return D}function ge(){return Vn()&&Sn}var en,vn;function cn(){if(!u(en)){en=!1;let n=/ Edg\/([\.0-9]+)/.exec(E.userAgent);n!==null&&(en=!0,vn=k(n[1]))}return en}function Se(){return cn()&&vn}var on,Rn;function fn(){if(!u(on)){on=!1;let n=/Firefox\/([\.0-9]+)/.exec(E.userAgent);n!==null&&(on=!0,Rn=k(n[1]))}return on}var hn;function ze(){return u(hn)||(hn=/Windows/i.test(E.appVersion)),hn}var wn;function Re(){return u(wn)||(wn=navigator.platform==="iPhone"||navigator.platform==="iPod"||navigator.platform==="iPad"),wn}function Me(){return fn()&&Rn}var jn;function qe(){return u(jn)||(jn=!fn()&&typeof PointerEvent<"u"&&(!u(E.pointerEnabled)||E.pointerEnabled)),jn}var $n,tn;function Dn(){if(!u(tn)){let n=document.createElement("canvas");n.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");let e=n.style.imageRendering;tn=u(e)&&e!=="",tn&&($n=e)}return tn}function Ee(){return Dn()?$n:void 0}function q(){if(!q.initialized)throw new g("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return q._result}q._promise=void 0;q._result=void 0;q.initialize=function(){return u(q._promise)||(q._promise=new Promise(n=>{let e=new Image;e.onload=function(){q._result=e.width>0&&e.height>0,n(q._result)},e.onerror=function(){q._result=!1,n(q._result)},e.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"})),q._promise};Object.defineProperties(q,{initialized:{get:function(){return u(q._result)}}});var v=[];typeof ArrayBuffer<"u"&&(v.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),typeof Uint8ClampedArray<"u"&&v.push(Uint8ClampedArray),typeof Uint8ClampedArray<"u"&&v.push(Uint8ClampedArray),typeof BigInt64Array<"u"&&v.push(BigInt64Array),typeof BigUint64Array<"u"&&v.push(BigUint64Array));var l={isChrome:zn,chromeVersion:he,isSafari:Ln,safariVersion:we,isWebkit:Wn,webkitVersion:je,isInternetExplorer:Vn,internetExplorerVersion:ge,isEdge:cn,edgeVersion:Se,isFirefox:fn,firefoxVersion:Me,isWindows:ze,isIPadOrIOS:Re,hardwareConcurrency:E.hardwareConcurrency??3,supportsPointerEvents:qe,supportsImageRenderingPixelated:Dn,supportsWebP:q,imageRenderingValue:Ee,typedArrayTypes:v};l.supportsBasis=function(n){return l.supportsWebAssembly()&&n.context.supportsBasis};l.supportsFullscreen=function(){return Un.supportsFullscreen()};l.supportsTypedArrays=function(){return typeof ArrayBuffer<"u"};l.supportsBigInt64Array=function(){return typeof BigInt64Array<"u"};l.supportsBigUint64Array=function(){return typeof BigUint64Array<"u"};l.supportsBigInt=function(){return typeof BigInt<"u"};l.supportsWebWorkers=function(){return typeof Worker<"u"};l.supportsWebAssembly=function(){return typeof WebAssembly<"u"};l.supportsWebgl2=function(n){return c.defined("scene",n),n.context.webgl2};l.supportsEsmWebWorkers=function(){return!fn()||parseInt(Rn)>=114};var go=l;export{y as a,$ as b,yn as c,oo as d,uo as e,yo as f,go as g};
