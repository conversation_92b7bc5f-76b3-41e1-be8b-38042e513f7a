/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as e}from"./chunk-HSVYYGKG.js";import{a as r}from"./chunk-65CN64EP.js";import"./chunk-3BNGKZ2F.js";import"./chunk-X76FJHF4.js";import"./chunk-NYNUONZK.js";import"./chunk-5YCKGPZ3.js";import"./chunk-S3NLG5WM.js";import"./chunk-AAOMPF7M.js";import"./chunk-PFXLBIMV.js";import"./chunk-PSBTKXXJ.js";import"./chunk-UJOKCDQH.js";import"./chunk-KYREMICR.js";import"./chunk-VBRVI5XI.js";import"./chunk-5ZFOKSDK.js";import"./chunk-7L2LUDC3.js";import"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import"./chunk-PX3QTMVS.js";import"./chunk-FE4HG5RY.js";function n(o,t){let m=e.unpackCombineGeometryParameters(o),i=e.combineGeometry(m);return e.packCombineGeometryResults(i,t)}var c=r(n);export{c as default};
