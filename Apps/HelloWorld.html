<!doctype html>
<html lang="en">
  <head>
    <!-- Use correct character set. -->
    <meta charset="utf-8" />
    <!-- Tell <PERSON> to use the latest, best version. -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Make the application on mobile take up the full browser screen and disable user scaling. -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <title>Hello World!</title>
    <script src="../Build/CesiumUnminified/Cesium.js"></script>
    <style>
      @import url(../Build/CesiumUnminified/Widgets/widgets.css);
      html,
      body,
      #cesiumContainer {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <div id="cesiumContainer"></div>
    <script>
      window.startup = async function (Cesium) {
        const viewer = new Cesium.Viewer("cesiumContainer");

        try {
          // Load SPZ-based Gaussian Splats tileset
          // The Demo tileset already uses KHR_spz_gaussian_splats_compression extension
          console.log("Starting to load SPZ tileset...");

          const tileset = await Cesium.Cesium3DTileset.fromUrl(
            "./SampleData/Cesium3DTiles/Demo/tileset.json",
            {
              // Add memory management options to prevent "Invalid array length" errors
              maximumMemoryUsage: 512, // Limit memory usage to 512MB
              maximumScreenSpaceError: 16, // Reduce detail for better performance
              skipLevelOfDetail: true,
              baseScreenSpaceError: 1024
            }
          );

          viewer.scene.primitives.add(tileset);

          // Wait for tileset to be ready before zooming
          tileset.readyPromise.then(() => {
            viewer.zoomTo(tileset);
            console.log("SPZ-based tileset loaded successfully");
            console.log("Tileset details:", {
              ready: tileset.ready,
              memoryUsage: tileset.totalMemoryUsageInBytes,
              tilesLoaded: tileset.tilesLoaded,
              hasGaussianSplats: tileset.gaussianSplatPrimitive !== undefined
            });
          });

        } catch (error) {
          console.error("Error loading SPZ tileset:", error);

          // Show user-friendly error message
          const errorDiv = document.createElement('div');
          errorDiv.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            max-width: 400px;
            text-align: center;
          `;

          if (error.message.includes("Invalid array length")) {
            errorDiv.innerHTML = `
              <h3>Memory Error</h3>
              <p>The SPZ file is too large for available memory. This is a known issue with large Gaussian Splats files.</p>
              <p>Try using a smaller SPZ file or increase browser memory limits.</p>
            `;
          } else {
            errorDiv.innerHTML = `
              <h3>Loading Error</h3>
              <p>Failed to load SPZ tileset: ${error.message}</p>
              <p>Make sure you're running this from a web server (not file:// protocol).</p>
            `;
          }

          document.body.appendChild(errorDiv);
        }
      }
    </script>
  </body>
</html>
