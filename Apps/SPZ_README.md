# SPZ Tileset Loading in Cesium

本文档说明如何在Cesium中加载基于SPZ（Splat Point Cloud）压缩的3D Tiles。

## 什么是SPZ？

SPZ是一种用于压缩Gaussian Splats数据的格式，通过glTF扩展`KHR_spz_gaussian_splats_compression`实现。这种格式可以高效地存储和传输3D Gaussian Splatting数据。

## 修改内容

### 1. HelloWorld.html 修改

原始代码已经在加载SPZ格式的tileset，但添加了错误处理和日志记录：

```javascript
window.startup = async function (Cesium) {
  const viewer = new Cesium.Viewer("cesiumContainer");

  try {
    // Load SPZ-based Gaussian Splats tileset
    // The Demo tileset already uses KHR_spz_gaussian_splats_compression extension
    const tileset = await Cesium.Cesium3DTileset.fromUrl(
      "./SampleData/Cesium3DTiles/Demo/tileset.json"
    );

    viewer.scene.primitives.add(tileset);
    viewer.zoomTo(tileset);

    console.log("SPZ-based tileset loaded successfully");
  } catch (error) {
    console.error("Error loading SPZ tileset:", error);
  }
}
```

### 2. 新增 SPZTilesetExample.html

创建了一个更完整的示例，展示：
- 加载本地SPZ tileset
- 加载Cesium Ion上的Gaussian Splats
- 错误处理和状态显示
- 清除tileset功能

## SPZ Tileset 特征

SPZ格式的tileset具有以下特征：

1. **扩展声明**：在tileset.json中声明使用`KHR_spz_gaussian_splats_compression`扩展
2. **3DTILES_content_gltf**：使用此扩展来指定glTF内容格式
3. **自动检测**：Cesium会自动检测并处理SPZ格式

### 示例tileset.json结构：

```json
{
  "asset": {
    "version": "1.1"
  },
  "extensions": {
    "3DTILES_content_gltf": {
      "extensionsRequired": ["KHR_spz_gaussian_splats_compression"],
      "extensionsUsed": ["KHR_spz_gaussian_splats_compression"]
    }
  },
  "extensionsUsed": ["3DTILES_content_gltf"],
  "geometricError": 65536,
  "root": {
    "boundingVolume": {
      "box": [...]
    },
    "geometricError": 32768,
    "refine": "REPLACE",
    "children": [
      {
        "content": {
          "uri": "content.glb"
        },
        "geometricError": 0
      }
    ]
  }
}
```

## 使用方法

### 基本加载

```javascript
const tileset = await Cesium.Cesium3DTileset.fromUrl("path/to/spz/tileset.json");
viewer.scene.primitives.add(tileset);
viewer.zoomTo(tileset);
```

### 从Cesium Ion加载

```javascript
const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(assetId);
viewer.scene.primitives.add(tileset);
viewer.zoomTo(tileset);
```

### 错误处理

```javascript
try {
  const tileset = await Cesium.Cesium3DTileset.fromUrl(url);
  viewer.scene.primitives.add(tileset);
} catch (error) {
  console.error("Failed to load SPZ tileset:", error);
}
```

## 故障排除

### "Invalid array length" 错误

这是加载大型SPZ文件时的常见错误，通常由以下原因引起：

1. **内存不足**：SPZ文件太大，超出了可用内存
2. **浏览器限制**：浏览器对单个数组的大小有限制
3. **文件损坏**：SPZ数据可能已损坏

**解决方案**：
```javascript
const tileset = await Cesium.Cesium3DTileset.fromUrl(url, {
  maximumMemoryUsage: 512, // 限制内存使用
  maximumScreenSpaceError: 16, // 降低细节级别
  skipLevelOfDetail: true,
  baseScreenSpaceError: 1024
});
```

### 文件访问错误

确保从Web服务器运行，而不是直接打开HTML文件（file://协议）。

**启动本地服务器**：
```bash
cd Apps
python3 -m http.server 8080
# 然后访问 http://localhost:8080/HelloWorld.html
```

### 性能优化

对于大型SPZ文件：
1. 使用`maximumMemoryUsage`限制内存
2. 增加`maximumScreenSpaceError`降低细节
3. 启用`skipLevelOfDetail`
4. 考虑使用较小的SPZ文件进行测试

## 注意事项

1. **实验性功能**：SPZ支持是实验性的，可能会在未来版本中发生变化
2. **浏览器兼容性**：确保浏览器支持WebGL和相关的现代Web标准
3. **性能**：Gaussian Splats渲染可能对GPU有较高要求
4. **内存管理**：大型SPZ文件可能需要大量内存，建议监控内存使用情况

## 快速开始

### 1. 启动Web服务器

**Linux/macOS**:
```bash
cd Apps
./start_server.sh
```

**Windows**:
```cmd
cd Apps
start_server.bat
```

**手动启动**:
```bash
cd Apps
python3 -m http.server 8080
```

### 2. 访问示例

- **基本示例**: http://localhost:8080/HelloWorld.html
- **完整示例**: http://localhost:8080/SPZTilesetExample.html

### 3. 测试不同的加载选项

在SPZTilesetExample.html中，你可以测试：
- 加载本地SPZ Demo（可能因文件过大而失败）
- 加载小型SPZ测试文件
- 从Cesium Ion加载Gaussian Splats
- 加载常规3D Tiles作为备用方案

## 测试

1. 打开 `Apps/HelloWorld.html` 查看基本的SPZ tileset加载
2. 打开 `Apps/SPZTilesetExample.html` 查看更完整的示例
3. 检查浏览器控制台查看加载状态和错误信息
4. 如果遇到"Invalid array length"错误，尝试使用"Load Small SPZ Test"或"Load Regular 3D Tiles"按钮

## 相关文件

- `Apps/HelloWorld.html` - 基本示例
- `Apps/SPZTilesetExample.html` - 完整示例
- `Apps/SampleData/Cesium3DTiles/Demo/tileset.json` - SPZ格式的示例tileset
- `Apps/SampleData/Cesium3DTiles/Demo/content.glb` - SPZ压缩的glTF内容
