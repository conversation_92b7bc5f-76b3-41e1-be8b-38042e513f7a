# SPZ Tileset Loading in Cesium

本文档说明如何在Cesium中加载基于SPZ（Splat Point Cloud）压缩的3D Tiles。

## 什么是SPZ？

SPZ是一种用于压缩Gaussian Splats数据的格式，通过glTF扩展`KHR_spz_gaussian_splats_compression`实现。这种格式可以高效地存储和传输3D Gaussian Splatting数据。

## 修改内容

### 1. HelloWorld.html 修改

原始代码已经在加载SPZ格式的tileset，但添加了错误处理和日志记录：

```javascript
window.startup = async function (Cesium) {
  const viewer = new Cesium.Viewer("cesiumContainer");

  try {
    // Load SPZ-based Gaussian Splats tileset
    // The Demo tileset already uses KHR_spz_gaussian_splats_compression extension
    const tileset = await Cesium.Cesium3DTileset.fromUrl(
      "./SampleData/Cesium3DTiles/Demo/tileset.json"
    );

    viewer.scene.primitives.add(tileset);
    viewer.zoomTo(tileset);
    
    console.log("SPZ-based tileset loaded successfully");
  } catch (error) {
    console.error("Error loading SPZ tileset:", error);
  }
}
```

### 2. 新增 SPZTilesetExample.html

创建了一个更完整的示例，展示：
- 加载本地SPZ tileset
- 加载Cesium Ion上的Gaussian Splats
- 错误处理和状态显示
- 清除tileset功能

## SPZ Tileset 特征

SPZ格式的tileset具有以下特征：

1. **扩展声明**：在tileset.json中声明使用`KHR_spz_gaussian_splats_compression`扩展
2. **3DTILES_content_gltf**：使用此扩展来指定glTF内容格式
3. **自动检测**：Cesium会自动检测并处理SPZ格式

### 示例tileset.json结构：

```json
{
  "asset": {
    "version": "1.1"
  },
  "extensions": {
    "3DTILES_content_gltf": {
      "extensionsRequired": ["KHR_spz_gaussian_splats_compression"],
      "extensionsUsed": ["KHR_spz_gaussian_splats_compression"]
    }
  },
  "extensionsUsed": ["3DTILES_content_gltf"],
  "geometricError": 65536,
  "root": {
    "boundingVolume": {
      "box": [...]
    },
    "geometricError": 32768,
    "refine": "REPLACE",
    "children": [
      {
        "content": {
          "uri": "content.glb"
        },
        "geometricError": 0
      }
    ]
  }
}
```

## 使用方法

### 基本加载

```javascript
const tileset = await Cesium.Cesium3DTileset.fromUrl("path/to/spz/tileset.json");
viewer.scene.primitives.add(tileset);
viewer.zoomTo(tileset);
```

### 从Cesium Ion加载

```javascript
const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(assetId);
viewer.scene.primitives.add(tileset);
viewer.zoomTo(tileset);
```

### 错误处理

```javascript
try {
  const tileset = await Cesium.Cesium3DTileset.fromUrl(url);
  viewer.scene.primitives.add(tileset);
} catch (error) {
  console.error("Failed to load SPZ tileset:", error);
}
```

## 注意事项

1. **实验性功能**：SPZ支持是实验性的，可能会在未来版本中发生变化
2. **浏览器兼容性**：确保浏览器支持WebGL和相关的现代Web标准
3. **性能**：Gaussian Splats渲染可能对GPU有较高要求

## 测试

1. 打开 `Apps/HelloWorld.html` 查看基本的SPZ tileset加载
2. 打开 `Apps/SPZTilesetExample.html` 查看更完整的示例
3. 检查浏览器控制台查看加载状态和错误信息

## 相关文件

- `Apps/HelloWorld.html` - 基本示例
- `Apps/SPZTilesetExample.html` - 完整示例
- `Apps/SampleData/Cesium3DTiles/Demo/tileset.json` - SPZ格式的示例tileset
- `Apps/SampleData/Cesium3DTiles/Demo/content.glb` - SPZ压缩的glTF内容
