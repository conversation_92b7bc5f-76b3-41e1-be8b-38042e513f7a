#!/bin/bash

# Simple script to start a local web server for testing SPZ tilesets

echo "Starting local web server for Cesium SPZ examples..."
echo "This will serve the Apps directory on http://localhost:8080"
echo ""
echo "Available examples:"
echo "- http://localhost:8080/HelloWorld.html (Basic SPZ loading)"
echo "- http://localhost:8080/SPZTilesetExample.html (Advanced SPZ examples)"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Check if Python 3 is available
if command -v python3 &> /dev/null; then
    echo "Using Python 3..."
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "Using Python 2..."
    python -m SimpleHTTPServer 8080
else
    echo "Error: Python not found. Please install Python to run the web server."
    echo "Alternatively, you can use any other web server to serve the Apps directory."
    exit 1
fi
