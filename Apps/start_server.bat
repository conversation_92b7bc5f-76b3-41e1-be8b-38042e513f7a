@echo off

echo Starting local web server for Cesium SPZ examples...
echo This will serve the Apps directory on http://localhost:8080
echo.
echo Available examples:
echo - http://localhost:8080/HelloWorld.html (Basic SPZ loading)
echo - http://localhost:8080/SPZTilesetExample.html (Advanced SPZ examples)
echo.
echo Press Ctrl+C to stop the server
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python...
    python -m http.server 8080
) else (
    echo Error: Python not found. Please install Python to run the web server.
    echo Alternatively, you can use any other web server to serve the Apps directory.
    pause
    exit /b 1
)
