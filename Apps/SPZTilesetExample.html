<!doctype html>
<html lang="en">
  <head>
    <!-- Use correct character set. -->
    <meta charset="utf-8" />
    <!-- Tell <PERSON> to use the latest, best version. -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Make the application on mobile take up the full browser screen and disable user scaling. -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <title>SPZ Tileset Loading Example</title>
    <script src="../Build/CesiumUnminified/Cesium.js"></script>
    <style>
      @import url(../Build/CesiumUnminified/Widgets/widgets.css);
      html,
      body,
      #cesiumContainer {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }

      #toolbar {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(42, 42, 42, 0.8);
        padding: 10px;
        border-radius: 5px;
        color: white;
        font-family: sans-serif;
        font-size: 14px;
      }

      #toolbar button {
        margin: 5px;
        padding: 8px 12px;
        background: #48b;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
      }

      #toolbar button:hover {
        background: #369;
      }

      #status {
        margin-top: 10px;
        font-size: 12px;
        color: #ccc;
      }
    </style>
  </head>
  <body>
    <div id="cesiumContainer"></div>
    <div id="toolbar">
      <h3>SPZ Tileset Examples</h3>
      <button onclick="loadLocalSPZ()">Load Local SPZ Demo</button>
      <button onclick="loadSmallSPZ()">Load Small SPZ Test</button>
      <button onclick="loadGaussianSplatsFromIon()">Load Gaussian Splats from Ion</button>
      <button onclick="loadRegularTileset()">Load Regular 3D Tiles (Fallback)</button>
      <button onclick="clearTilesets()">Clear All Tilesets</button>
      <div id="status">Ready to load SPZ tilesets...</div>
    </div>

    <script>
      let viewer;
      let currentTilesets = [];

      window.startup = async function (Cesium) {
        // Initialize the Cesium viewer
        viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
          // Enable experimental features for SPZ support
          experimentalFeatures: true
        });

        updateStatus("Cesium viewer initialized. Ready to load SPZ tilesets.");
      };

      function updateStatus(message) {
        document.getElementById('status').textContent = message;
        console.log(message);
      }

      async function loadLocalSPZ() {
        try {
          updateStatus("Loading local SPZ tileset...");

          // Check if the file exists and is accessible
          const tilesetUrl = "./SampleData/Cesium3DTiles/Demo/tileset.json";

          // First, try to fetch the tileset.json to check if it's accessible
          const response = await fetch(tilesetUrl);
          if (!response.ok) {
            throw new Error(`Failed to fetch tileset.json: ${response.status} ${response.statusText}`);
          }

          const tilesetJson = await response.json();
          console.log("Tileset JSON loaded:", tilesetJson);

          // Load the local SPZ-based tileset with additional options
          const tileset = await Cesium.Cesium3DTileset.fromUrl(tilesetUrl, {
            // Add memory management options
            maximumMemoryUsage: 512, // Limit memory usage to 512MB
            maximumScreenSpaceError: 16, // Reduce detail to improve performance
            skipLevelOfDetail: true,
            baseScreenSpaceError: 1024,
            skipScreenSpaceErrorFactor: 16,
            skipLevels: 1,
            immediatelyLoadDesiredLevelOfDetail: false,
            loadSiblings: false,
            cullWithChildrenBounds: true,
            // Enable debug logging
            debugShowBoundingVolume: false,
            debugShowContentBoundingVolume: false,
            debugShowViewerRequestVolume: false
          });

          // Add event listeners for debugging
          setupTilesetEventListeners(tileset);

          viewer.scene.primitives.add(tileset);
          currentTilesets.push(tileset);

          // Wait for the tileset to be ready before zooming
          await new Promise((resolve) => {
            const checkReady = () => {
              if (tileset.ready) {
                resolve();
              } else {
                setTimeout(checkReady, 100);
              }
            };
            checkReady();
          });

          // Zoom to the tileset with a nice viewing angle
          await viewer.zoomTo(tileset, new Cesium.HeadingPitchRange(
            0.0, // heading
            -0.5, // pitch (looking down slightly)
            Math.max(tileset.boundingSphere.radius * 2.0, 100.0) // distance
          ));

          updateStatus("Local SPZ tileset loaded successfully!");

          // Log detailed tileset information
          console.log("Tileset info:", {
            url: tileset.url,
            ready: tileset.ready,
            boundingSphere: tileset.boundingSphere,
            hasGaussianSplats: tileset.gaussianSplatPrimitive !== undefined,
            memoryUsage: tileset.totalMemoryUsageInBytes,
            tilesLoaded: tileset.tilesLoaded
          });

          logTilesetExtensions(tileset);

        } catch (error) {
          updateStatus(`Error loading local SPZ tileset: ${error.message}`);
          console.error("Detailed error:", error);
          console.error("Error stack:", error.stack);

          // Provide suggestions for common issues
          if (error.message.includes("Invalid array length")) {
            updateStatus("Error: Invalid array length - this may be due to memory constraints or corrupted SPZ data. Try using a smaller SPZ file or increase available memory.");
          } else if (error.message.includes("Failed to fetch")) {
            updateStatus("Error: Cannot access tileset file. Make sure you're running from a web server (not file:// protocol).");
          }
        }
      }

      async function loadSmallSPZ() {
        try {
          updateStatus("Loading small SPZ test tileset...");

          // Try to load a smaller SPZ tileset for testing
          const tileset = await Cesium.Cesium3DTileset.fromUrl(
            "./SampleData/Cesium3DTiles/SmallSPZ/tileset.json",
            {
              maximumMemoryUsage: 256, // Even smaller memory limit
              maximumScreenSpaceError: 32,
              skipLevelOfDetail: true
            }
          );

          setupTilesetEventListeners(tileset);
          viewer.scene.primitives.add(tileset);
          currentTilesets.push(tileset);

          await viewer.zoomTo(tileset);
          updateStatus("Small SPZ tileset loaded successfully!");

        } catch (error) {
          updateStatus(`Error loading small SPZ tileset: ${error.message}`);
          console.error("Detailed error:", error);
        }
      }

      async function loadGaussianSplatsFromIon() {
        try {
          updateStatus("Loading Gaussian Splats from Cesium Ion...");

          // Load Gaussian Splats tileset from Cesium Ion
          // These are example asset IDs for Gaussian Splats tilesets
          const tilesetId = 3443920; // Example Gaussian Splats asset ID

          const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(tilesetId, {
            maximumMemoryUsage: 1024, // Allow more memory for Ion assets
            maximumScreenSpaceError: 8
          });

          setupTilesetEventListeners(tileset);
          viewer.scene.primitives.add(tileset);
          currentTilesets.push(tileset);

          // Zoom to the tileset
          await viewer.zoomTo(tileset, new Cesium.HeadingPitchRange(
            Cesium.Math.toRadians(-50),
            Cesium.Math.toRadians(-20),
            100.0
          ));

          updateStatus("Gaussian Splats tileset from Ion loaded successfully!");

        } catch (error) {
          updateStatus(`Error loading Ion tileset: ${error.message}`);
          console.error("Detailed error:", error);
        }
      }

      async function loadRegularTileset() {
        try {
          updateStatus("Loading regular 3D Tiles as fallback...");

          // Load a regular (non-SPZ) tileset as fallback
          const tileset = await Cesium.Cesium3DTileset.fromUrl(
            "./SampleData/Cesium3DTiles/Batched/BatchedColors/tileset.json"
          );

          viewer.scene.primitives.add(tileset);
          currentTilesets.push(tileset);

          await viewer.zoomTo(tileset);
          updateStatus("Regular 3D Tiles loaded successfully!");

        } catch (error) {
          updateStatus(`Error loading regular tileset: ${error.message}`);
          console.error("Detailed error:", error);
        }
      }

      function clearTilesets() {
        // Remove all loaded tilesets
        currentTilesets.forEach(tileset => {
          viewer.scene.primitives.remove(tileset);
        });
        currentTilesets = [];
        updateStatus("All tilesets cleared.");
      }

      // Additional utility functions for SPZ tileset handling
      function logTilesetExtensions(tileset) {
        if (tileset.asset && tileset.asset.extensions) {
          console.log("Tileset extensions:", tileset.asset.extensions);
        }

        if (tileset.extensionsUsed) {
          console.log("Extensions used:", tileset.extensionsUsed);
        }

        if (tileset.extensionsRequired) {
          console.log("Extensions required:", tileset.extensionsRequired);
        }
      }

      // Event listeners for tileset loading events
      function setupTilesetEventListeners(tileset) {
        tileset.tileLoad.addEventListener(function(tile) {
          console.log("Tile loaded:", tile);
        });

        tileset.tileUnload.addEventListener(function(tile) {
          console.log("Tile unloaded:", tile);
        });

        tileset.tileFailed.addEventListener(function(event) {
          console.error("Tile failed to load:", event);
        });
      }
    </script>
  </body>
</html>
