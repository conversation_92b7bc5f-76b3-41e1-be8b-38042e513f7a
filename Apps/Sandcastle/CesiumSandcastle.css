@import url("../../ThirdParty/dojo-release-1.10.4/dijit/themes/claro/claro.css");

html,
body {
  height: 100%;
  margin: 0;
  overflow: hidden;
  padding: 0;
  font-family: sans-serif;
}

#loading {
  display: table;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 5000;
  background: #eee;
  border: none;
  text-align: center;
  color: #777;
}
#loading span {
  display: table-cell;
  vertical-align: middle;
}

#appLayout {
  width: 100%;
  height: 100%;
}

#toolbar,
#galleryContainer {
  -ms-user-select: none;
  -moz-user-select: -moz-none; /* allows for re-enabling on sub-elements like the search box */
  -webkit-user-select: none;
  user-select: none;
}

#search {
  -ms-user-select: text;
  -moz-user-select: text;
  -webkit-user-select: text;
  user-select: text;
}

.cesiumTitle {
  float: right;
}

#codeContainer {
  width: 40%;
}

#bottomPanel {
  height: 225px;
}

.galleryContainer {
  width: 100%;
}

.demosContainer {
  overflow: auto;
  background: rgba(0, 0, 0, 0.8);
}

.demos {
  padding: 0 5px 5px;
  white-space: nowrap;
}

.demoTileThumbnail {
  max-height: 150px;
  max-width: 225px;
  height: 100px;
  width: auto;
}

.demoTileTitle {
  margin: 0 auto 2px auto;
  font-size: 0.9em;
}

.demoTooltipType {
  text-align: center;
  color: #888;
  margin-bottom: 5px;
  padding-right: 10px;
}

.subInfo {
  font-size: 11px;
}

.galleryError {
  color: #f88;
}

#docPopup {
  display: block;
  position: absolute;
  left: -999px;
  top: 0;
  font-family: sans-serif;
  font-size: 10pt;
}
#docPopup a,
#docPopup a:visited {
  display: block;
  text-decoration: none;
  line-height: 12pt;
}
#docPopup a:focus,
#docPopup a:hover {
  text-decoration: underline;
}

.logContainer {
  display: block;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: scroll;
}

#logOutput {
  white-space: pre-wrap;
  padding: 2px 4px;
  font-size: 9pt;
  font-family: monospace;
}

.consoleError {
  color: #f00;
}

.fullFrame {
  border: none;
  width: 100%;
  height: 100%;
  transition-property: transform;
  transition-duration: 0.5s;
  transform-origin: 200px 152px; /* These numbers should be divisible by 4 because of scaling in .makeThumbnail */
}

.makeThumbnail {
  width: 900px;
  height: 600px;
  transform: scale(0.25);
}

.popDownDialog {
  width: 360px;
}

a.linkButton,
a.linkButton:visited {
  display: inline-block;
}

a.linkButton:focus,
a.linkButton:hover {
  text-decoration: none;
}

.CodeMirror,
.CodeMirror-scroll {
  height: 100%;
  font-size: 13px;
}

.highlightToolbarButton {
  background: #fe2;
  border-radius: 4px;
}

.claro .bottomPanel {
  padding: 0;
  overflow: hidden;
}

.bottomPanel #innerPanel_tablist {
  max-height: 28px;
  overflow: auto !important;
}

.feedback {
  width: 250px;

  .future-banner {
    display: flex;
    justify-content: center;
    align-items: stretch;
    text-align: center;
    flex-direction: column;

    h3 {
      margin: 0 1em;
    }
    p {
      margin: 1em 1.5em;
    }
  }
}

.claro .dijitTabContainerTop-tabs .dijitTabChecked .dijitTabContent {
  background-position: 0 -103px;
}

.claro .dijitTabContainerTop-dijitContentPane {
  padding: 0;
  overflow: hidden;
}

.mblScrollBarWrapper > div {
  background-color: #9ed3ff !important;
}

.errorMarker {
  width: 100%;
  height: 100%;
  color: #222;
  font-weight: bold;
  background-color: #f42;
  border-radius: 3px;
  border: none;
}

.CodeMirror pre.errorLine {
  background: rgba(200, 50, 0, 0.2);
}

.hintMarker {
  width: 100%;
  height: 100%;
  color: #222;
  font-weight: bold;
  background-color: #fe2;
  border-radius: 3px;
  border: none;
}

.CodeMirror pre.hintLine {
  background: rgba(200, 200, 0, 0.2);
}

.highlightMarker {
  width: 100%;
  height: 100%;
  color: #222;
  font-weight: bold;
  background-color: #2e2;
  border-radius: 3px;
  border: none;
}

.CodeMirror pre.highlightLine {
  background: rgba(0, 200, 0, 0.2);
}

.searchMarker {
  width: 100%;
  height: 100%;
  color: #222;
  font-weight: bold;
  background-color: #cef;
  border-radius: 3px;
  border: none;
}

.CodeMirror pre.searchLine {
  background: rgba(180, 230, 240, 0.2);
}

.CodeMirror-gutter-text {
  cursor: default;
}

.gitHubIcon {
  background-image: url("./images/gitHub16px.png");
  width: 16px;
  height: 16px;
  text-align: center;
}

.shareIcon {
  background-image: url("./images/share16px.png");
  width: 16px;
  height: 16px;
  text-align: center;
}
