<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw polylines with various widths and materials."
    />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const redLine = viewer.entities.add({
          name: "Red line on terrain",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray([-75, 35, -125, 35]),
            width: 5,
            material: Cesium.Color.RED,
            clampToGround: true,
          },
        });

        const greenRhumbLine = viewer.entities.add({
          name: "Green rhumb line",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray([-75, 35, -125, 35]),
            width: 5,
            arcType: Cesium.ArcType.RHUMB,
            material: Cesium.Color.GREEN,
          },
        });

        const glowingLine = viewer.entities.add({
          name: "Glowing blue line on the surface",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray([-75, 37, -125, 37]),
            width: 10,
            material: new Cesium.PolylineGlowMaterialProperty({
              glowPower: 0.2,
              taperPower: 0.5,
              color: Cesium.Color.CORNFLOWERBLUE,
            }),
          },
        });

        const orangeOutlined = viewer.entities.add({
          name: "Orange line with black outline at height and following the surface",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -75, 39, 250000, -125, 39, 250000,
            ]),
            width: 5,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.ORANGE,
              outlineWidth: 2,
              outlineColor: Cesium.Color.BLACK,
            }),
          },
        });

        const purpleArrow = viewer.entities.add({
          name: "Purple straight arrow at height",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -75, 43, 500000, -125, 43, 500000,
            ]),
            width: 10,
            arcType: Cesium.ArcType.NONE,
            material: new Cesium.PolylineArrowMaterialProperty(Cesium.Color.PURPLE),
          },
        });

        const dashedLine = viewer.entities.add({
          name: "Blue dashed line",
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -75, 45, 500000, -125, 45, 500000,
            ]),
            width: 4,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.CYAN,
            }),
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
