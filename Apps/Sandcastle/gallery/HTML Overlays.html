<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Add HTML overlay using canvas coordinates." />
    <meta name="cesium-sandcastle-labels" content="Tutorials" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <img
      id="htmlOverlay"
      style="position: absolute"
      src="../images/Cesium_Logo_overlay.png"
    />
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        // To geographically place an HTML element on top of the Cesium canvas, we use
        // scene.cartesianToCanvasCoordinates to map a world position to canvas x and y values.
        // This example places and img element, but any element will work.

        const htmlOverlay = document.getElementById("htmlOverlay");
        const scratch = new Cesium.Cartesian2();
        viewer.scene.preRender.addEventListener(function () {
          const position = Cesium.Cartesian3.fromDegrees(-75.59777, 40.03883);
          const canvasPosition = viewer.scene.cartesianToCanvasCoordinates(
            position,
            scratch,
          );
          if (Cesium.defined(canvasPosition)) {
            htmlOverlay.style.top = `${canvasPosition.y}px`;
            htmlOverlay.style.left = `${canvasPosition.x}px`;
          }
        });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
