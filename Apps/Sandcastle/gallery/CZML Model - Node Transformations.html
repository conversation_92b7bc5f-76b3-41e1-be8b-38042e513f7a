<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Model - Node Transformations" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Model",
            version: "1.0",
            clock: {
              interval: "2015-01-01T12:00:00Z/2015-01-01T12:00:20Z",
              currentTime: "2015-01-01T12:00:00Z",
              multiplier: 20,
            },
          },
          {
            id: "model",
            position: {
              cartographicDegrees: [-77, 37, 100000],
            },
            viewFrom: {
              cartesian: [4.3, 0.1, 2.6],
            },
            model: {
              gltf: "../../SampleData/models/CesiumMan/Cesium_Man.glb",
              runAnimations: false,
              nodeTransformations: {
                Skeleton_arm_joint_L__3_: {
                  rotation: {
                    epoch: "2015-01-01T12:00:00Z",
                    unitQuaternion: [
                      0, -0.23381920887303329, -0.6909886782144156, -0.0938384854833712,
                      0.6775378681547408, 10, -0.4924076887347565, -0.6304934596091216,
                      0.20657864059632378, 0.563327551886459, 20, -0.23381920887303329,
                      -0.6909886782144156, -0.0938384854833712, 0.6775378681547408,
                    ],
                  },
                },
                Skeleton_arm_joint_R__2_: {
                  rotation: {
                    unitQuaternion: [
                      -0.2840422631464792, -0.40211904424847345,
                      // eslint-disable-next-line no-loss-of-precision
                      0.25175867757399086, 0.7063888981321548,
                    ],
                  },
                },
              },
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        const dataSourcePromise = viewer.dataSources.add(
          Cesium.CzmlDataSource.load(czml),
        );

        dataSourcePromise
          .then(function (dataSource) {
            viewer.trackedEntity = dataSource.entities.getById("model");
          })
          .catch(function (error) {
            window.alert(error);
          }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
