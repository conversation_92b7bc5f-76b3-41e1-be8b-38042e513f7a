<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="A sample Vector dataset on terrain rendered with 3D Tiles."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, 3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // An example showing a point cloud tileset classified by a Geometry tileset.
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        try {
          // Point Cloud by Prof. Peter Allen, Columbia University Robotics Lab. Scanning by Alejandro Troccoli and Matei Ciocarlie.
          const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(16421);
          viewer.scene.primitives.add(tileset);

          // Geometry Tiles are experimental and the format is subject to change in the future.
          // For more details, see:
          //    https://github.com/CesiumGS/3d-tiles/tree/vctr/TileFormats/Geometry
          const classificationTileset = await Cesium.Cesium3DTileset.fromUrl(
            "../../SampleData/Cesium3DTiles/Classification/PointCloud/tileset.json",
            {
              classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
            },
          );
          viewer.scene.primitives.add(classificationTileset);

          classificationTileset.style = new Cesium.Cesium3DTileStyle({
            color: {
              conditions: [
                ["${id} === 'roof1'", "color('#004FFF', 0.5)"],
                ["${id} === 'towerBottom1'", "color('#33BB66', 0.5)"],
                ["${id} === 'towerTop1'", "color('#0099AA', 0.5)"],
                ["${id} === 'roof2'", "color('#004FFF', 0.5)"],
                ["${id} === 'tower3'", "color('#FF8833', 0.5)"],
                ["${id} === 'tower4'", "color('#FFAA22', 0.5)"],
                ["true", "color('#FFFF00', 0.5)"],
              ],
            },
          });
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        viewer.scene.camera.setView({
          destination: new Cesium.Cartesian3(
            4401744.644145314,
            225051.41078911052,
            4595420.374784433,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            5.646733805039757,
            -0.276607153839886,
            6.281110875400085,
          ),
        });

        // Information about the currently highlighted feature
        const highlighted = {
          feature: undefined,
          originalColor: new Cesium.Color(),
        };

        // Color a feature yellow on hover.
        viewer.screenSpaceEventHandler.setInputAction(function onMouseMove(movement) {
          // If a feature was previously highlighted, undo the highlight
          if (Cesium.defined(highlighted.feature)) {
            highlighted.feature.color = highlighted.originalColor;
            highlighted.feature = undefined;
          }

          // Pick a new feature
          const pickedFeature = viewer.scene.pick(movement.endPosition);
          if (!Cesium.defined(pickedFeature)) {
            return;
          }

          // Highlight the feature
          highlighted.feature = pickedFeature;
          Cesium.Color.clone(pickedFeature.color, highlighted.originalColor);
          pickedFeature.color = Cesium.Color.YELLOW.withAlpha(0.5);
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
