<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Attach a custom data source to the geocoder widget."
    />
    <meta name="cesium-sandcastle-labels" content="Tutorials,Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        /**
         * This class is an example of a custom geocoder. It provides geocoding through the OpenStreetMap Nominatim service.
         * @alias OpenStreetMapNominatimGeocoder
         * @constructor
         */
        function OpenStreetMapNominatimGeocoder() {}

        /**
         * The function called to geocode using this geocoder service.
         *
         * @param {string} input The query to be sent to the geocoder service
         * @returns {Promise<GeocoderService.Result[]>}
         */
        OpenStreetMapNominatimGeocoder.prototype.geocode = function (input) {
          const endpoint = "https://nominatim.openstreetmap.org/search";
          const resource = new Cesium.Resource({
            url: endpoint,
            queryParameters: {
              format: "json",
              q: input,
            },
          });

          return resource.fetchJson().then(function (results) {
            let bboxDegrees;
            return results.map(function (resultObject) {
              bboxDegrees = resultObject.boundingbox;
              return {
                displayName: resultObject.display_name,
                destination: Cesium.Rectangle.fromDegrees(
                  bboxDegrees[2],
                  bboxDegrees[0],
                  bboxDegrees[3],
                  bboxDegrees[1],
                ),
              };
            });
          });
        };

        const viewer = new Cesium.Viewer("cesiumContainer", {
          geocoder: new OpenStreetMapNominatimGeocoder(),
        });

        viewer.geocoder.viewModel.searchText = "Philadelphia";
        viewer.geocoder.viewModel.search();

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
