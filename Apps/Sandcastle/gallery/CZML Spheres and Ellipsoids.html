<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Spheres and Ellipsoids" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Geometries: Spheres and Ellipsoids",
            version: "1.0",
          },
          {
            id: "blueEllipsoid",
            name: "blue ellipsoid",
            position: {
              cartographicDegrees: [-114.0, 40.0, 300000.0],
            },
            ellipsoid: {
              radii: {
                cartesian: [200000.0, 200000.0, 300000.0],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [0, 0, 255, 255],
                  },
                },
              },
            },
          },
          {
            id: "redSphere",
            name: "Red sphere with black outline",
            position: {
              cartographicDegrees: [-107.0, 40.0, 300000.0],
            },
            ellipsoid: {
              radii: {
                cartesian: [300000.0, 300000.0, 300000.0],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 0, 0, 100],
                  },
                },
              },
              outline: true,
              outlineColor: {
                rgbaf: [0, 0, 0, 1],
              },
            },
          },
          {
            id: "yellowEllipsoid",
            name: "ellipsoid with yellow outline",
            position: {
              cartographicDegrees: [-100.0, 40.0, 300000.0],
            },
            ellipsoid: {
              radii: {
                cartesian: [200000.0, 200000.0, 300000.0],
              },
              fill: false,
              outline: true,
              outlineColor: {
                rgba: [255, 255, 0, 255],
              },
              slicePartitions: 24,
              stackPartitions: 36,
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer");
        const dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        viewer.zoomTo(dataSourcePromise);

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
