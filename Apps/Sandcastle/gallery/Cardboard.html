<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Configure viewer to add a button enabling look at a mobile device with cardboard."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          vrButton: true,
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });
        // Click the VR button in the bottom right of the screen to switch to VR mode.

        viewer.scene.globe.enableLighting = true;
        viewer.scene.globe.depthTestAgainstTerrain = true;

        // Follow the path of a plane. See the interpolation Sandcastle example.
        Cesium.Math.setRandomNumberSeed(3);

        const start = Cesium.JulianDate.fromDate(new Date(2015, 2, 25, 16));
        const stop = Cesium.JulianDate.addSeconds(start, 360, new Cesium.JulianDate());

        viewer.clock.startTime = start.clone();
        viewer.clock.stopTime = stop.clone();
        viewer.clock.currentTime = start.clone();
        viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
        viewer.clock.multiplier = 1.0;
        viewer.clock.shouldAnimate = true;

        function computeCirclularFlight(lon, lat, radius) {
          const property = new Cesium.SampledPositionProperty();
          const startAngle = Cesium.Math.nextRandomNumber() * 360.0;
          const endAngle = startAngle + 360.0;

          const increment = (Cesium.Math.nextRandomNumber() * 2.0 - 1.0) * 10.0 + 45.0;
          for (let i = startAngle; i < endAngle; i += increment) {
            const radians = Cesium.Math.toRadians(i);
            const timeIncrement = i - startAngle;
            const time = Cesium.JulianDate.addSeconds(
              start,
              timeIncrement,
              new Cesium.JulianDate(),
            );
            const position = Cesium.Cartesian3.fromDegrees(
              lon + radius * 1.5 * Math.cos(radians),
              lat + radius * Math.sin(radians),
              Cesium.Math.nextRandomNumber() * 500 + 1800,
            );
            property.addSample(time, position);
          }
          return property;
        }

        const longitude = -112.110693;
        const latitude = 36.0994841;
        const radius = 0.03;

        const modelURI = "../../SampleData/models/CesiumBalloon/CesiumBalloon.glb";
        const entity = viewer.entities.add({
          availability: new Cesium.TimeIntervalCollection([
            new Cesium.TimeInterval({
              start: start,
              stop: stop,
            }),
          ]),
          position: computeCirclularFlight(longitude, latitude, radius),
          model: {
            uri: modelURI,
            minimumPixelSize: 64,
          },
        });

        entity.position.setInterpolationOptions({
          interpolationDegree: 2,
          interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
        });

        // Set initial camera position and orientation to be when in the model's reference frame.
        const camera = viewer.camera;
        camera.position = new Cesium.Cartesian3(0.25, 0.0, 0.0);
        camera.direction = new Cesium.Cartesian3(1.0, 0.0, 0.0);
        camera.up = new Cesium.Cartesian3(0.0, 0.0, 1.0);
        camera.right = new Cesium.Cartesian3(0.0, -1.0, 0.0);

        viewer.scene.postUpdate.addEventListener(function (scene, time) {
          const position = entity.position.getValue(time);
          if (!Cesium.defined(position)) {
            return;
          }

          let transform;
          if (!Cesium.defined(entity.orientation)) {
            transform = Cesium.Transforms.eastNorthUpToFixedFrame(position);
          } else {
            const orientation = entity.orientation.getValue(time);
            if (!Cesium.defined(orientation)) {
              return;
            }

            transform = Cesium.Matrix4.fromRotationTranslation(
              Cesium.Matrix3.fromQuaternion(orientation),
              position,
            );
          }

          // Save camera state
          const offset = Cesium.Cartesian3.clone(camera.position);
          const direction = Cesium.Cartesian3.clone(camera.direction);
          const up = Cesium.Cartesian3.clone(camera.up);

          // Set camera to be in model's reference frame.
          camera.lookAtTransform(transform);

          // Reset the camera state to the saved state so it appears fixed in the model's frame.
          Cesium.Cartesian3.clone(offset, camera.position);
          Cesium.Cartesian3.clone(direction, camera.direction);
          Cesium.Cartesian3.clone(up, camera.up);
          Cesium.Cartesian3.cross(direction, up, camera.right);
        });

        // Add a few more balloons flying with the one the viewer is in.
        const numBalloons = 12;
        for (let i = 0; i < numBalloons; ++i) {
          const balloonRadius =
            (Cesium.Math.nextRandomNumber() * 2.0 - 1.0) * 0.01 + radius;
          const balloon = viewer.entities.add({
            availability: new Cesium.TimeIntervalCollection([
              new Cesium.TimeInterval({
                start: start,
                stop: stop,
              }),
            ]),
            position: computeCirclularFlight(longitude, latitude, balloonRadius),
            model: {
              uri: modelURI,
              minimumPixelSize: 64,
            },
          });

          balloon.position.setInterpolationOptions({
            interpolationDegree: 2,
            interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
          });
        } //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
