<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw a polyline volume, which is a shape extruded along a polyline."
    />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        function computeCircle(radius) {
          const positions = [];
          for (let i = 0; i < 360; i++) {
            const radians = Cesium.Math.toRadians(i);
            positions.push(
              new Cesium.Cartesian2(
                radius * Math.cos(radians),
                radius * Math.sin(radians),
              ),
            );
          }
          return positions;
        }

        function computeStar(arms, rOuter, rInner) {
          const angle = Math.PI / arms;
          const length = 2 * arms;
          const positions = new Array(length);
          for (let i = 0; i < length; i++) {
            const r = i % 2 === 0 ? rOuter : rInner;
            positions[i] = new Cesium.Cartesian2(
              Math.cos(i * angle) * r,
              Math.sin(i * angle) * r,
            );
          }
          return positions;
        }

        const redTube = viewer.entities.add({
          name: "Red tube with rounded corners",
          polylineVolume: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -85.0, 32.0, -85.0, 36.0, -89.0, 36.0,
            ]),
            shape: computeCircle(60000.0),
            material: Cesium.Color.RED,
          },
        });

        const greenBox = viewer.entities.add({
          name: "Green box with beveled corners and outline",
          polylineVolume: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -90.0, 32.0, 0.0, -90.0, 36.0, 100000.0, -94.0, 36.0, 0.0,
            ]),
            shape: [
              new Cesium.Cartesian2(-50000, -50000),
              new Cesium.Cartesian2(50000, -50000),
              new Cesium.Cartesian2(50000, 50000),
              new Cesium.Cartesian2(-50000, 50000),
            ],
            cornerType: Cesium.CornerType.BEVELED,
            material: Cesium.Color.GREEN.withAlpha(0.5),
            outline: true,
            outlineColor: Cesium.Color.BLACK,
          },
        });

        const blueStar = viewer.entities.add({
          name: "Blue star with mitered corners and outline",
          polylineVolume: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -95.0, 32.0, 0.0, -95.0, 36.0, 100000.0, -99.0, 36.0, 200000.0,
            ]),
            shape: computeStar(7, 70000, 50000),
            cornerType: Cesium.CornerType.MITERED,
            material: Cesium.Color.BLUE,
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
