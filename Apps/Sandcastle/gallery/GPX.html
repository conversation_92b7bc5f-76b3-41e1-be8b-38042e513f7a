<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="A simple GPX example." />
    <meta name="cesium-sandcastle-labels" content="Showcases, DataSources" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        const pinBuilder = new Cesium.PinBuilder();

        Sandcastle.addToolbarMenu(
          [
            {
              text: "Track with Waypoints",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.GpxDataSource.load("../../SampleData/gpx/lamina.gpx", {
                      clampToGround: true,
                    }),
                  )
                  .then(function (dataSource) {
                    viewer.zoomTo(dataSource.entities);
                  });
              },
            },
            {
              text: "Route",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.GpxDataSource.load("../../SampleData/gpx/route.gpx", {
                      clampToGround: true,
                    }),
                  )
                  .then(function (dataSource) {
                    viewer.zoomTo(dataSource.entities);
                  });
              },
            },
            {
              text: "Waypoints",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.GpxDataSource.load("../../SampleData/gpx/wpt.gpx", {
                      clampToGround: true,
                    }),
                  )
                  .then(function (dataSource) {
                    viewer.zoomTo(dataSource.entities);
                  });
              },
            },
            {
              text: "Multiple Tracks with Waypoints",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.GpxDataSource.load("../../SampleData/gpx/complexTrk.gpx", {
                      clampToGround: true,
                    }),
                  )
                  .then(function (dataSource) {
                    viewer.zoomTo(dataSource.entities);
                  });
              },
            },
            {
              text: "Symbology Options",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.GpxDataSource.load("../../SampleData/gpx/lamina.gpx", {
                      clampToGround: true,
                      trackColor: Cesium.Color.YELLOW,
                      waypointImage: pinBuilder.fromMakiIconId(
                        "bicycle",
                        Cesium.Color.BLUE,
                        48,
                      ),
                    }),
                  )
                  .then(function (dataSource) {
                    viewer.zoomTo(dataSource.entities);
                  });
              },
            },
          ],
          "toolbar",
        );

        Sandcastle.reset = function () {
          viewer.dataSources.removeAll();
          viewer.clock.clockRange = Cesium.ClockRange.UNBOUNDED;
          viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK;
        };
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
