<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Custom post processing effect." />
    <meta name="cesium-sandcastle-labels" content="Showcases, Post Processing" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        const position = Cesium.Cartesian3.fromDegrees(-123.0744619, 44.0503706);
        const url = "../../SampleData/models/CesiumMan/Cesium_Man.glb";
        viewer.trackedEntity = viewer.entities.add({
          name: url,
          position: position,
          model: {
            uri: url,
          },
        });

        const fragmentShaderSource = `
          uniform sampler2D colorTexture;
          in vec2 v_textureCoordinates;
          const int KERNEL_WIDTH = 16;
          void main(void)
          {
              vec2 step = czm_pixelRatio / czm_viewport.zw;
              vec2 integralPos = v_textureCoordinates - mod(v_textureCoordinates, 8.0 * step);
              vec3 averageValue = vec3(0.0);
              for (int i = 0; i < KERNEL_WIDTH; i++)
              {
                  for (int j = 0; j < KERNEL_WIDTH; j++)
                  {
                      averageValue += texture(colorTexture, integralPos + step * vec2(i, j)).rgb;
                  }
              }
              averageValue /= float(KERNEL_WIDTH * KERNEL_WIDTH);
              out_FragColor = vec4(averageValue, 1.0);
          }
          `;
        viewer.scene.postProcessStages.add(
          new Cesium.PostProcessStage({
            fragmentShader: fragmentShaderSource,
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
