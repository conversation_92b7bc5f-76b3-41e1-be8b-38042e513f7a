<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Aerometrex San Francisco High Resolution 3D Model with Street Level Enhanced 3D, available on ion until March 15, 2023."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, ion Assets" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        // Aerometrex San Francisco High Resolution 3D Model with Street Level Enhanced 3D
        const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(1415196, {
          maximumScreenSpaceError: 4,
        });
        viewer.scene.primitives.add(tileset);

        // Ferry Building – Street Level Enhanced mesh with a resolution of ~6mm per pixel
        function viewFerryBuilding(duration) {
          viewer.scene.camera.flyTo({
            destination: new Cesium.Cartesian3(
              -2703541.419456986,
              -4261164.971874713,
              3887416.257562123,
            ),
            orientation: new Cesium.HeadingPitchRoll(
              5.959123393581913,
              -0.03131876941215883,
              0.0000033030489428043097,
            ),
            duration: duration,
          });
        }

        // Pier 39 - 2 cm/pixel resolution
        function viewPier39() {
          viewer.scene.camera.flyTo({
            destination: new Cesium.Cartesian3(
              -2704263.584923937,
              -4259336.981155519,
              3888978.5732662966,
            ),
            orientation: new Cesium.HeadingPitchRoll(
              2.4287691459386607,
              -0.49459905591668996,
              0.0000029701571779838787,
            ),
          });
        }

        // Skyline - 2 cm/pixel resolution
        function viewSkyline() {
          viewer.scene.camera.flyTo({
            destination: new Cesium.Cartesian3(
              -2702979.5635104137,
              -4261981.190435306,
              3887092.144148863,
            ),
            orientation: new Cesium.HeadingPitchRoll(
              4.534265054628527,
              -0.08846186652294352,
              0.0000075141499165098935,
            ),
          });
        }

        // Lombard Street - 2 cm/pixel resolution
        function viewLombardStreet() {
          viewer.scene.camera.flyTo({
            destination: new Cesium.Cartesian3(
              -2705631.6783492276,
              -4259449.36938678,
              3887903.89229016,
            ),
            orientation: new Cesium.HeadingPitchRoll(
              5.999439616451804,
              -0.20513082834763674,
              4.7213266807233367e-7,
            ),
          });
        }

        Sandcastle.addToolbarMenu([
          {
            text: "Ferry Building",
            onselect: viewFerryBuilding,
          },
          {
            text: "Pier 39",
            onselect: viewPier39,
          },
          {
            text: "Skyline",
            onselect: viewSkyline,
          },
          {
            text: "Lombard Street",
            onselect: viewLombardStreet,
          },
        ]);

        viewFerryBuilding(0);
        //Sandcastle_End
        Sandcastle.finishedLoading();
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
