<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="A simple KML example." />
    <meta name="cesium-sandcastle-labels" content="Showcases, DataSources" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        const options = {
          camera: viewer.scene.camera,
          canvas: viewer.scene.canvas,
        };

        let tour = null;
        viewer.dataSources
          .add(
            Cesium.KmlDataSource.load(
              "../../SampleData/kml/eiffel-tower-flyto.kml",
              options,
            ),
          )
          .then(function (dataSource) {
            tour = dataSource.kmlTours[0];
            tour.tourStart.addEventListener(function () {
              console.log("Start tour");
            });
            tour.tourEnd.addEventListener(function (terminated) {
              console.log(`${terminated ? "Terminate" : "End"} tour`);
            });
            tour.entryStart.addEventListener(function (entry) {
              console.log(`Play ${entry.type} (${entry.duration})`);
            });
            tour.entryEnd.addEventListener(function (entry, terminated) {
              console.log(`${terminated ? "Terminate" : "End"} ${entry.type}`);
            });
          });

        Sandcastle.addToolbarButton("Play", function () {
          tour.play(viewer.cesiumWidget);
        });

        Sandcastle.addToolbarButton("Terminate", function () {
          tour.stop();
        });

        Sandcastle.reset = function () {
          viewer.dataSources.removeAll();
          viewer.clock.clockRange = Cesium.ClockRange.UNBOUNDED;
          viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK;
        };
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
