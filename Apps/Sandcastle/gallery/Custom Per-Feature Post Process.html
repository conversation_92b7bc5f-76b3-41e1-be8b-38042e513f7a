<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Custom per-feature post processing effect." />
    <meta name="cesium-sandcastle-labels" content="Showcases, Post Processing" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });
        const scene = viewer.scene;

        const position = Cesium.Cartesian3.fromDegrees(-123.0744619, 44.0503706);
        const url = "../../SampleData/models/CesiumMan/Cesium_Man.glb";
        const entity = (viewer.trackedEntity = viewer.entities.add({
          name: url,
          position: position,
          model: {
            uri: url,
          },
        }));

        // Shade selected model with highlight.
        const fragmentShaderSource = `
          uniform sampler2D colorTexture;
          in vec2 v_textureCoordinates;
          uniform vec4 highlight;
          void main() {
              vec4 color = texture(colorTexture, v_textureCoordinates);
              if (czm_selected()) {
                  vec3 highlighted = highlight.a * highlight.rgb + (1.0 - highlight.a) * color.rgb;
                  out_FragColor = vec4(highlighted, 1.0);
              } else {
                  out_FragColor = color;
              }
          }
          `;

        const stage = scene.postProcessStages.add(
          new Cesium.PostProcessStage({
            fragmentShader: fragmentShaderSource,
            uniforms: {
              highlight: function () {
                return new Cesium.Color(1.0, 0.0, 0.0, 0.5);
              },
            },
          }),
        );
        stage.selected = [];

        const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
        handler.setInputAction(function (movement) {
          const pickedObject = viewer.scene.pick(movement.endPosition);
          if (Cesium.defined(pickedObject)) {
            stage.selected = [pickedObject.primitive];
          } else {
            stage.selected = [];
          }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
