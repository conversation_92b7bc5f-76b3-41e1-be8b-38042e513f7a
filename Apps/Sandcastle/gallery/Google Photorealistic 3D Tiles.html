<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Use Viewer to start building new applications or easily embed Cesium into existing applications."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          timeline: false,
          animation: false,
          sceneModePicker: false,
          baseLayerPicker: false,
          geocoder: Cesium.IonGeocodeProviderType.GOOGLE,
          // The globe does not need to be displayed,
          // since the Photorealistic 3D Tiles include terrain
          globe: false,
        });

        // Enable rendering the sky
        viewer.scene.skyAtmosphere.show = true;

        // Add Photorealistic 3D Tiles
        try {
          const tileset = await Cesium.createGooglePhotorealistic3DTileset({
            // Only the Google Geocoder can be used with Google Photorealistic 3D Tiles.  Set the `geocode` property of the viewer constructor options to IonGeocodeProviderType.GOOGLE.
            onlyUsingWithGoogleGeocoder: true,
          });
          viewer.scene.primitives.add(tileset);
        } catch (error) {
          console.log(`Error loading Photorealistic 3D Tiles tileset.
          ${error}`);
        }

        // Point the camera at the Googleplex
        viewer.scene.camera.setView({
          destination: new Cesium.Cartesian3(
            -2693797.551060477,
            -4297135.517094725,
            3854700.**********,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            4.6550106925119925,
            -0.2863894863138836,
            1.3561760425773173e-7,
          ),
        }); //Sandcastle_End
        Sandcastle.finishedLoading();
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
      }
    </script>
  </body>
</html>
