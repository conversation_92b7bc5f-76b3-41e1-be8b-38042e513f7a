<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw a rectangle or extruded rectangle that conforms to the surface of the globe."
    />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const redRectangle = viewer.entities.add({
          name: "Red translucent rectangle",
          rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(-110.0, 20.0, -80.0, 25.0),
            material: Cesium.Color.RED.withAlpha(0.5),
          },
        });

        const greenRectangle = viewer.entities.add({
          name: "Green translucent, rotated, and extruded rectangle at height with outline",
          rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(-110.0, 30.0, -100.0, 40.0),
            material: Cesium.Color.GREEN.withAlpha(0.5),
            rotation: Cesium.Math.toRadians(45),
            extrudedHeight: 300000.0,
            height: 100000.0,
            outline: true, // height must be set for outline to display
            outlineColor: Cesium.Color.BLACK,
          },
        });

        let rotation = Cesium.Math.toRadians(30);

        function getRotationValue() {
          rotation += 0.005;
          return rotation;
        }
        viewer.entities.add({
          name: "Rotating rectangle with rotating texture coordinate",
          rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(-92.0, 30.0, -76.0, 40.0),
            material: "../images/Cesium_Logo_Color.jpg",
            rotation: new Cesium.CallbackProperty(getRotationValue, false),
            stRotation: new Cesium.CallbackProperty(getRotationValue, false),
            classificationType: Cesium.ClassificationType.TERRAIN,
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
