<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Render a time dynamic point cloud from a set of Point Cloud tiles and timestamps."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        const dates = [
          "2018-07-19T15:18:00Z",
          "2018-07-19T15:18:00.5Z",
          "2018-07-19T15:18:01Z",
          "2018-07-19T15:18:01.5Z",
          "2018-07-19T15:18:02Z",
          "2018-07-19T15:18:02.5Z",
        ];

        const uris = [
          "../../SampleData/Cesium3DTiles/PointCloud/PointCloudTimeDynamic/0.pnts",
          "../../SampleData/Cesium3DTiles/PointCloud/PointCloudTimeDynamic/1.pnts",
          "../../SampleData/Cesium3DTiles/PointCloud/PointCloudTimeDynamic/2.pnts",
          "../../SampleData/Cesium3DTiles/PointCloud/PointCloudTimeDynamic/3.pnts",
          "../../SampleData/Cesium3DTiles/PointCloud/PointCloudTimeDynamic/4.pnts",
        ];

        function dataCallback(interval, index) {
          return {
            uri: uris[index],
          };
        }

        const timeIntervalCollection = Cesium.TimeIntervalCollection.fromIso8601DateArray(
          {
            iso8601Dates: dates,
            dataCallback: dataCallback,
          },
        );

        const pointCloud = new Cesium.TimeDynamicPointCloud({
          intervals: timeIntervalCollection,
          clock: viewer.clock,
          style: new Cesium.Cesium3DTileStyle({
            pointSize: 5,
          }),
        });
        viewer.scene.primitives.add(pointCloud);

        const start = Cesium.JulianDate.fromIso8601(dates[0]);
        const stop = Cesium.JulianDate.fromIso8601(dates[dates.length - 1]);

        viewer.timeline.zoomTo(start, stop);

        const clock = viewer.clock;
        clock.startTime = start;
        clock.currentTime = start;
        clock.stopTime = stop;
        clock.clockRange = Cesium.ClockRange.LOOP_STOP;

        viewer.zoomTo(pointCloud, new Cesium.HeadingPitchRange(0.0, -0.5, 50.0));
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
