<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Add MSAA to the scene." />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        viewer.clock.currentTime = Cesium.JulianDate.fromIso8601("2022-08-01T00:00:00Z");

        const scene = viewer.scene;
        if (!scene.msaaSupported) {
          window.alert("This browser does not support MSAA.");
        }
        scene.msaaSamples = 8;

        function createModel(url, height) {
          const position = Cesium.Cartesian3.fromDegrees(
            -123.0744619,
            44.0503706,
            height,
          );
          const heading = Cesium.Math.toRadians(135);
          const pitch = 0;
          const roll = 0;
          const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
          const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);

          const entity = viewer.entities.add({
            name: url,
            position: position,
            orientation: orientation,
            model: {
              uri: url,
              minimumPixelSize: 128,
              maximumScale: 20000,
            },
          });
          const target = Cesium.Cartesian3.fromDegrees(
            -123.0744619,
            44.0503706,
            height + 7.5,
          );
          const offset = new Cesium.Cartesian3(50.0, -15.0, 0.0);
          viewer.scene.camera.lookAt(target, offset);
        }

        let currentAssetId;
        async function createTileset(assetId) {
          currentAssetId = assetId;

          try {
            const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(assetId);

            if (currentAssetId !== assetId) {
              // Another scenario was loaded. Discard the result.
              return;
            }

            scene.primitives.add(tileset);
          } catch (error) {
            console.log(`Error loading tileset: ${error}`);
          }
        }

        const options = [
          {
            text: "Statue of Liberty",
            onselect: function () {
              viewer.entities.removeAll();
              scene.primitives.removeAll();
              scene.camera.setView({
                destination: new Cesium.Cartesian3(
                  1331419.302230775,
                  -4656681.5022043325,
                  4136232.6465900405,
                ),
                orientation: new Cesium.HeadingPitchRoll(
                  6.032455545102689,
                  -0.056832496140112765,
                  6.282360923090216,
                ),
                endTransform: Cesium.Matrix4.IDENTITY,
              });
              createTileset(75343);
            },
          },
          {
            text: "3D Tiles BIM",
            onselect: function () {
              viewer.entities.removeAll();
              scene.primitives.removeAll();
              viewer.camera.setView({
                destination: new Cesium.Cartesian3(
                  1234138.7804841248,
                  -5086063.633843134,
                  3633284.606361642,
                ),
                orientation: {
                  heading: 0.4304630387656614,
                  pitch: -0.16969316864215878,
                  roll: 6.283184816241989,
                },
              });
              createTileset(2464651);
            },
          },
          {
            text: "Hot Air Balloon",
            onselect: function () {
              currentAssetId = undefined;
              viewer.entities.removeAll();
              scene.primitives.removeAll();
              createModel(
                "../../SampleData/models/CesiumBalloon/CesiumBalloon.glb",
                1000.0,
              );
            },
          },
        ];

        const samplingOptions = [
          {
            text: "MSAA 8x",
            onselect: function () {
              scene.msaaSamples = 8;
            },
          },
          {
            text: "MSAA 4x",
            onselect: function () {
              scene.msaaSamples = 4;
            },
          },
          {
            text: "MSAA 2x",
            onselect: function () {
              scene.msaaSamples = 2;
            },
          },
          {
            text: "MSAA off",
            onselect: function () {
              scene.msaaSamples = 1;
            },
          },
        ];
        Sandcastle.addToolbarMenu(options);
        Sandcastle.addToolbarMenu(samplingOptions);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
