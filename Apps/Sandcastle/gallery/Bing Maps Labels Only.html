<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Use Cesium ion's Bing Maps Labels Only imagery asset to add labels to unlabeled imagery."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);

      #slider {
        position: absolute;
        left: 50%;
        top: 0px;
        background-color: #d3d3d3;
        width: 5px;
        height: 100%;
        z-index: 9999;
      }

      #slider:hover {
        cursor: ew-resize;
      }
    </style>

    <div id="cesiumContainer" class="fullSize">
      <div id="slider"></div>
    </div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // This example showcases the ability to overlay labels
        // on top of unlabeled imagery.
        //
        // On the left side is Bing Maps Aerial With Labels + unlabeled high
        // resolution Washington DC imagery. The labels are obscured by the
        // DC imagery and can not be turned on or off independently.
        //
        // On the right side is Bing Maps Aerial + unlabeled high resolution
        // Washington DC imagery + Bing Maps Labels Only. The labels
        // are now on top of all imagery in the scene and can be independently
        // shown or hidden based on app configuration and/or camera zoom level.

        // For this demo, start with all imagery disabled.
        const viewer = new Cesium.Viewer("cesiumContainer", {
          baseLayer: false,
          baseLayerPicker: false,
          infoBox: false,
          geocoder: Cesium.IonGeocodeProviderType.BING,
        });

        const layers = viewer.imageryLayers;

        // Add Bing Maps Aerial with Labels to the left panel
        const bingMapsAerialWithLabels = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.IonImageryProvider.fromAssetId(3),
        );
        bingMapsAerialWithLabels.splitDirection = Cesium.SplitDirection.LEFT;
        layers.add(bingMapsAerialWithLabels);

        // Add Bing Maps Aerial (unlabeled) to the right panel
        const bingMapsAerial = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.IonImageryProvider.fromAssetId(2),
        );
        bingMapsAerial.splitDirection = Cesium.SplitDirection.RIGHT;
        layers.add(bingMapsAerial);

        // Add high resolution Washington DC imagery to both panels.
        const imageryLayer = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.IonImageryProvider.fromAssetId(3827),
        );
        viewer.imageryLayers.add(imageryLayer);

        // Add Bing Maps Labels Only to the right panel
        const bingMapsLabelsOnly = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.IonImageryProvider.fromAssetId(2411391),
        );
        bingMapsLabelsOnly.splitDirection = Cesium.SplitDirection.RIGHT; // Only show to the left of the slider.
        layers.add(bingMapsLabelsOnly);

        // Zoom to the Washington DC imagery
        viewer.zoomTo(imageryLayer);

        // Add a button to toggle the display of the Bing Maps Labels Only layer
        Sandcastle.addToggleButton("Show Bing Maps Labels Only", true, (checked) => {
          bingMapsLabelsOnly.show = checked;
        });

        // The remaining code synchronizes the position of the slider with the split position
        const slider = document.getElementById("slider");
        viewer.scene.splitPosition = slider.offsetLeft / slider.parentElement.offsetWidth;

        const handler = new Cesium.ScreenSpaceEventHandler(slider);

        let moveActive = false;

        function move(movement) {
          if (!moveActive) {
            return;
          }

          const relativeOffset = movement.endPosition.x;
          const splitPosition =
            (slider.offsetLeft + relativeOffset) / slider.parentElement.offsetWidth;
          slider.style.left = `${100.0 * splitPosition}%`;
          viewer.scene.splitPosition = splitPosition;
        }

        handler.setInputAction(function () {
          moveActive = true;
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
        handler.setInputAction(function () {
          moveActive = true;
        }, Cesium.ScreenSpaceEventType.PINCH_START);

        handler.setInputAction(move, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handler.setInputAction(move, Cesium.ScreenSpaceEventType.PINCH_MOVE);

        handler.setInputAction(function () {
          moveActive = false;
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
        handler.setInputAction(function () {
          moveActive = false;
        }, Cesium.ScreenSpaceEventType.PINCH_END);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
