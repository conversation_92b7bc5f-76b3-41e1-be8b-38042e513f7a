<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Display polylines on BIM and Photogrammetry 3D Tiles"
    />
    <meta name="cesium-sandcastle-labels" content="3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Power Plant design model provided by Bentley Systems
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        viewer.clock.currentTime = Cesium.JulianDate.fromIso8601("2022-08-01T00:00:00Z");

        scene.camera.setView({
          destination: new Cesium.Cartesian3(
            1234151.4883992162,
            -5086036.79436967,
            3633328.4278331124,
          ),
          orientation: {
            heading: 5.593695742186853,
            pitch: -1.0786797635545216,
            roll: 6.27892466154778,
          },
        });

        let powerPlant;
        let powerPlantShow = true;
        try {
          powerPlant = await Cesium.Cesium3DTileset.fromIonAssetId(2464651);
          powerPlant.show = powerPlantShow;
          scene.primitives.add(powerPlant);
          powerPlant.tileLoad.addEventListener(function (tile) {
            processTileFeatures(tile, hideDuplicateFloor);
          });
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        // Hide duplicate floor geometry to avoid
        // z-fighting artifacts.
        const duplicateFloor = 114082;
        function getElement(feature) {
          return parseInt(feature.getProperty("element"), 10);
        }

        function hideDuplicateFloor(feature) {
          const element = parseInt(feature.getProperty("element"), 10);

          if (element === duplicateFloor) {
            feature.show = false;
          }
        }

        function processContentFeatures(content, callback) {
          const featuresLength = content.featuresLength;
          for (let i = 0; i < featuresLength; ++i) {
            const feature = content.getFeature(i);
            callback(feature);
          }
        }

        function processTileFeatures(tile, callback) {
          const content = tile.content;
          const innerContents = content.innerContents;
          if (Cesium.defined(innerContents)) {
            const length = innerContents.length;
            for (let i = 0; i < length; ++i) {
              processContentFeatures(innerContents[i], callback);
            }
          } else {
            processContentFeatures(content, callback);
          }
        }

        const pipes = viewer.entities.add({
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -76.36053390920833, 34.949935893493596, -76.36055481641581,
              34.94993589886988, -76.36055477047704, 34.94992280693651,
            ]),
            width: 6,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.YELLOW,
              dashLength: 20.0,
            }),
            show: true,
            clampToGround: true,
            classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
          },
        });

        let building;
        try {
          building = await Cesium.Cesium3DTileset.fromIonAssetId(40866);
          building.show = !powerPlantShow;
          scene.primitives.add(building);
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        const route = viewer.entities.add({
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -75.59604807301078, 40.03948512841901, -75.59644577413066,
              40.039316280505446, -75.59584544997564, 40.03846271524258,
              -75.59661425371488, 40.03814087821916, -75.59664726332451,
              40.03818297772907,
            ]),
            width: 6,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.YELLOW,
            }),
            show: false,
            clampToGround: true,
            classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
          },
        });

        Sandcastle.addDefaultToolbarMenu([
          {
            text: "BIM",
            onselect: function () {
              powerPlantShow = true;

              pipes.polyline.show = powerPlantShow;
              route.polyline.show = !powerPlantShow;
              if (Cesium.defined(powerPlant)) {
                powerPlant.show = powerPlantShow;
              }
              if (Cesium.defined(building)) {
                building.show = !powerPlantShow;
              }

              scene.camera.setView({
                destination: new Cesium.Cartesian3(
                  1234151.4883992162,
                  -5086036.79436967,
                  3633328.4278331124,
                ),
                orientation: {
                  heading: 5.593695742186853,
                  pitch: -1.0786797635545216,
                  roll: 6.27892466154778,
                },
              });
            },
          },
          {
            text: "Photogrammetry",
            onselect: function () {
              powerPlantShow = false;

              pipes.polyline.show = powerPlantShow;
              route.polyline.show = !powerPlantShow;
              if (Cesium.defined(powerPlant)) {
                powerPlant.show = powerPlantShow;
              }
              if (Cesium.defined(building)) {
                building.show = !powerPlantShow;
              }

              scene.camera.setView({
                destination: new Cesium.Cartesian3(
                  1216596.5376729995,
                  -4736445.416889214,
                  4081406.990364228,
                ),
                orientation: {
                  heading: 5.153176564030707,
                  pitch: -0.9701972964526693,
                  roll: 6.277883257569513,
                },
              });
            },
          },
        ]); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
