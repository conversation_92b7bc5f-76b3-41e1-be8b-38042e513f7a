<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Change the projection between perspective and orthographic."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Click the projection picker to switch between orthographic and perspective projections.
        const viewer = new Cesium.Viewer("cesiumContainer", {
          projectionPicker: true,
        });

        // start with orthographic projection
        viewer.projectionPicker.viewModel.switchToOrthographic();

        const position = Cesium.Cartesian3.fromDegrees(-123.0744619, 44.0503706, 0.0);
        const hpr = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(135), 0.0, 0.0);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);

        const entity = viewer.entities.add({
          position: position,
          orientation: orientation,
          model: {
            uri: "../../SampleData/models/CesiumMilkTruck/CesiumMilkTruck.glb",
            minimumPixelSize: 128,
            maximumScale: 20000,
          },
        });
        viewer.trackedEntity = entity;
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
