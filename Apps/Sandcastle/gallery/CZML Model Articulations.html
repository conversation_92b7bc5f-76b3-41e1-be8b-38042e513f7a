<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Model Articulations" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Model",
            version: "1.0",
            clock: {
              interval: "2019-06-01T16:00:00Z/2019-06-01T16:10:00Z",
              currentTime: "2019-06-01T16:00:00Z",
              multiplier: 60,
              range: "LOOP_STOP",
              step: "SYSTEM_CLOCK_MULTIPLIER",
            },
          },
          {
            id: "test model",
            name: "Cesium Air",
            position: {
              cartographicDegrees: [-77, 37, 10000],
            },
            model: {
              gltf: "https://cesium.com/public/SandcastleSampleData/launchvehicle.glb",
              scale: 2.0,
              minimumPixelSize: 128,
              runAnimations: false,
              articulations: {
                "Fairing Open": {
                  epoch: "2019-06-01T16:00:00Z",
                  number: [0, 0, 600, 120],
                },
                "Fairing Separate": {
                  epoch: "2019-06-01T16:00:00Z",
                  number: [0, 0, 400, -50],
                },
                "Fairing Drop": {
                  epoch: "2019-06-01T16:00:00Z",
                  interpolationAlgorithm: "LAGRANGE",
                  interpolationDegree: 2,
                  number: [0, 0, 80, 0, 100, 0, 120, -1, 600, -120],
                },
              },
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        const dataSourcePromise = viewer.dataSources.add(
          Cesium.CzmlDataSource.load(czml),
        );

        dataSourcePromise
          .then(function (dataSource) {
            viewer.trackedEntity = dataSource.entities.getById("test model");
          })
          .catch(function (error) {
            console.error(error);
          });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
