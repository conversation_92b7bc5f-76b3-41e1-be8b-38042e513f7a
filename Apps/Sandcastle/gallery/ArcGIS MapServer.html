<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Add imagery from an ArcGIS MapServer." />
    <meta name="cesium-sandcastle-labels" content="Beginner" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // An ArcGIS Access Token is required to authenticate requests to an ArcGIS Image Tile service.
        // To access secure ArcGIS resources, you need to create an ArcGIS developer account at https://developers.arcgis.com/,
        // then implement an authentication method to obtain an access token. See https://developers.arcgis.com/documentation/mapping-apis-and-services/security

        // The access token can be assigned globally:
        // Cesium.ArcGisMapService.defaultAccessToken = <token>;
        // or as a token parameter when creating the ArcGisMapServeImageryProvider.
        // const viewer = new Cesium.Viewer("cesiumContainer", {
        //   baseLayer: Cesium.ImageryLayer.fromProviderAsync(
        //     Cesium.ArcGisMapServerImageryProvider.fromBasemapType(
        //       Cesium.ArcGisBaseMapType.SATELLITE, {
        //         token: "<token>"
        //   })),
        // });
        const viewer = new Cesium.Viewer("cesiumContainer", {
          baseLayer: Cesium.ImageryLayer.fromProviderAsync(
            Cesium.ArcGisMapServerImageryProvider.fromBasemapType(
              Cesium.ArcGisBaseMapType.SATELLITE,
              // other supported styles include:
              // Cesium.ArcGisMapServerImageryProvider.HILLSHADE
              // Cesium.ArcGisMapServerImageryProvider.OCEANS
            ),
          ),
        });

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
