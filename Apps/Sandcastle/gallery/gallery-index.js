// This file is automatically rebuilt by the Cesium build process.
const hello_world_index = 126;
const VERSION = '1.130.1';
const gallery_demos = [{
  "name": "3D Models",
  "isNew": false,
  "img": "3D Models.jpg"
}, {
  "name": "3D Models Coloring",
  "isNew": false,
  "img": "3D Models Coloring.jpg"
}, {
  "name": "3D Tiles 1.1 CDB Yemen",
  "isNew": false,
  "img": "3D Tiles 1.1 CDB Yemen.jpg"
}, {
  "name": "3D Tiles 1.1 Photogrammetry",
  "isNew": false,
  "img": "3D Tiles 1.1 Photogrammetry.jpg"
}, {
  "name": "3D Tiles 1.1 Photogrammetry Classification",
  "isNew": false,
  "img": "3D Tiles 1.1 Photogrammetry Classification.jpg"
}, {
  "name": "3D Tiles 1.1 S2 Globe",
  "isNew": false,
  "img": "3D Tiles 1.1 S2 Globe.jpg"
}, {
  "name": "3D Tiles Adjust Height",
  "isNew": false,
  "img": "3D Tiles Adjust Height.jpg"
}, {
  "name": "3D Tiles BIM",
  "isNew": false,
  "img": "3D Tiles BIM.jpg"
}, {
  "name": "3D Tiles Batch Table Hierarchy",
  "isNew": false,
  "img": "3D Tiles Batch Table Hierarchy.jpg"
}, {
  "name": "3D Tiles Clipping Planes",
  "isNew": false,
  "img": "3D Tiles Clipping Planes.jpg"
}, {
  "name": "3D Tiles Compare",
  "isNew": false,
  "img": "3D Tiles Compare.jpg"
}, {
  "name": "3D Tiles Feature Picking",
  "isNew": false,
  "img": "3D Tiles Feature Picking.jpg"
}, {
  "name": "3D Tiles Feature Styling",
  "isNew": false,
  "img": "3D Tiles Feature Styling.jpg"
}, {
  "name": "3D Tiles Formats",
  "isNew": false,
  "img": "3D Tiles Formats.jpg"
}, {
  "name": "3D Tiles Gaussian Splatting",
  "isNew": true,
  "img": "3D Tiles Gaussian Splatting.jpg"
}, {
  "name": "3D Tiles Inspector",
  "isNew": false,
  "img": "3D Tiles Inspector.jpg"
}, {
  "name": "3D Tiles Interactivity",
  "isNew": false,
  "img": "3D Tiles Interactivity.jpg"
}, {
  "name": "3D Tiles Interior",
  "isNew": false,
  "img": "3D Tiles Interior.jpg"
}, {
  "name": "3D Tiles NGA GPM Visualization",
  "isNew": false,
  "img": "3D Tiles NGA GPM Visualization.jpg"
}, {
  "name": "3D Tiles Photogrammetry",
  "isNew": false,
  "img": "3D Tiles Photogrammetry.jpg"
}, {
  "name": "3D Tiles Photogrammetry Classification",
  "isNew": false,
  "img": "3D Tiles Photogrammetry Classification.jpg"
}, {
  "name": "3D Tiles Point Cloud",
  "isNew": false,
  "img": "3D Tiles Point Cloud.jpg"
}, {
  "name": "3D Tiles Point Cloud Classification",
  "isNew": false,
  "img": "3D Tiles Point Cloud Classification.jpg"
}, {
  "name": "3D Tiles Point Cloud Shading",
  "isNew": false,
  "img": "3D Tiles Point Cloud Shading.jpg"
}, {
  "name": "3D Tiles Point Cloud Styling",
  "isNew": false,
  "img": "3D Tiles Point Cloud Styling.jpg"
}, {
  "name": "3D Tiles Terrain Classification",
  "isNew": false,
  "img": "3D Tiles Terrain Classification.jpg"
}, {
  "name": "3D Tiles Vertical Exaggeration",
  "isNew": false,
  "img": "3D Tiles Vertical Exaggeration.jpg"
}, {
  "name": "AEC Architectural Design",
  "isNew": false,
  "img": "AEC Architectural Design.jpg"
}, {
  "name": "AEC Clipping",
  "isNew": false,
  "img": "AEC Clipping.jpg"
}, {
  "name": "AEC Isolate by Category",
  "isNew": false,
  "img": "AEC Isolate by Category.jpg"
}, {
  "name": "AEC Metadata Styling",
  "isNew": false,
  "img": "AEC Metadata Styling.jpg"
}, {
  "name": "Aerometrex San Francisco",
  "isNew": false,
  "img": "Aerometrex San Francisco.jpg"
}, {
  "name": "Ambient Occlusion",
  "isNew": false,
  "img": "Ambient Occlusion.jpg"
}, {
  "name": "ArcGIS MapServer",
  "isNew": false,
  "img": "ArcGIS MapServer.jpg"
}, {
  "name": "ArcGIS Tiled Elevation Terrain",
  "isNew": false,
  "img": "ArcGIS Tiled Elevation Terrain.jpg"
}, {
  "name": "ArcticDEM",
  "isNew": false,
  "img": "ArcticDEM.jpg"
}, {
  "name": "Atmosphere",
  "isNew": false,
  "img": "Atmosphere.jpg"
}, {
  "name": "Bathymetry",
  "isNew": false,
  "img": "Bathymetry.jpg"
}, {
  "name": "Billboards",
  "isNew": false,
  "img": "Billboards.jpg"
}, {
  "name": "Bing Maps Labels Only",
  "isNew": false,
  "img": "Bing Maps Labels Only.jpg"
}, {
  "name": "Bloom",
  "isNew": false,
  "img": "Bloom.jpg"
}, {
  "name": "Blue Marble",
  "isNew": false,
  "img": "Blue Marble.jpg"
}, {
  "name": "Box",
  "isNew": false,
  "img": "Box.jpg"
}, {
  "name": "CZML",
  "isNew": false,
  "img": "CZML.jpg"
}, {
  "name": "CZML 3D Tiles",
  "isNew": false,
  "img": "CZML 3D Tiles.jpg"
}, {
  "name": "CZML Billboard and Label",
  "isNew": false,
  "img": "CZML Billboard and Label.jpg"
}, {
  "name": "CZML Box",
  "isNew": false,
  "img": "CZML Box.jpg"
}, {
  "name": "CZML Circles and Ellipses",
  "isNew": false,
  "img": "CZML Circles and Ellipses.jpg"
}, {
  "name": "CZML Colors",
  "isNew": false,
  "img": "CZML Colors.jpg"
}, {
  "name": "CZML Cones and Cylinders",
  "isNew": false,
  "img": "CZML Cones and Cylinders.jpg"
}, {
  "name": "CZML Corridor",
  "isNew": false,
  "img": "CZML Corridor.jpg"
}, {
  "name": "CZML Custom Properties",
  "isNew": false,
  "img": "CZML Custom Properties.jpg"
}, {
  "name": "CZML Model",
  "isNew": false,
  "img": "CZML Model.jpg"
}, {
  "name": "CZML Model - Node Transformations",
  "isNew": false,
  "img": "CZML Model - Node Transformations.jpg"
}, {
  "name": "CZML Model Articulations",
  "isNew": false,
  "img": "CZML Model Articulations.jpg"
}, {
  "name": "CZML Model Data URL",
  "isNew": false,
  "img": "CZML Model Data URL.jpg"
}, {
  "name": "CZML Path",
  "isNew": false,
  "img": "CZML Path.jpg"
}, {
  "name": "CZML Point",
  "isNew": false,
  "img": "CZML Point.jpg"
}, {
  "name": "CZML Point - Time Dynamic",
  "isNew": false,
  "img": "CZML Point - Time Dynamic.jpg"
}, {
  "name": "CZML Polygon",
  "isNew": false,
  "img": "CZML Polygon.jpg"
}, {
  "name": "CZML Polygon - Interpolating References",
  "isNew": false,
  "img": "CZML Polygon - Interpolating References.jpg"
}, {
  "name": "CZML Polygon - Intervals, Availability",
  "isNew": false,
  "img": "CZML Polygon - Intervals, Availability.jpg"
}, {
  "name": "CZML Polyline",
  "isNew": false,
  "img": "CZML Polyline.jpg"
}, {
  "name": "CZML Polyline Volume",
  "isNew": false,
  "img": "CZML Polyline Volume.jpg"
}, {
  "name": "CZML Position Definitions",
  "isNew": false,
  "img": "CZML Position Definitions.jpg"
}, {
  "name": "CZML Rectangle",
  "isNew": false,
  "img": "CZML Rectangle.jpg"
}, {
  "name": "CZML Reference Properties",
  "isNew": false,
  "img": "CZML Reference Properties.jpg"
}, {
  "name": "CZML Spheres and Ellipsoids",
  "isNew": false,
  "img": "CZML Spheres and Ellipsoids.jpg"
}, {
  "name": "CZML Wall",
  "isNew": false,
  "img": "CZML Wall.jpg"
}, {
  "name": "CZML ZIndex",
  "isNew": false,
  "img": "CZML ZIndex.jpg"
}, {
  "name": "Callback Position Property",
  "isNew": false,
  "img": "Callback Position Property.jpg"
}, {
  "name": "Callback Property",
  "isNew": false,
  "img": "Callback Property.jpg"
}, {
  "name": "Camera",
  "isNew": false,
  "img": "Camera.jpg"
}, {
  "name": "Camera Tutorial",
  "isNew": false,
  "img": "Camera Tutorial.jpg"
}, {
  "name": "Cardboard",
  "isNew": false,
  "img": "Cardboard.jpg"
}, {
  "name": "Cartographic Limit Rectangle",
  "isNew": false,
  "img": "Cartographic Limit Rectangle.jpg"
}, {
  "name": "Cesium Inspector",
  "isNew": false,
  "img": "Cesium Inspector.jpg"
}, {
  "name": "Cesium OSM Buildings",
  "isNew": false,
  "img": "Cesium OSM Buildings.jpg"
}, {
  "name": "Cesium Widget",
  "isNew": false,
  "img": "Cesium Widget.jpg"
}, {
  "name": "Cesium World Terrain",
  "isNew": false,
  "img": "Cesium World Terrain.jpg"
}, {
  "name": "Circles and Ellipses",
  "isNew": false,
  "img": "Circles and Ellipses.jpg"
}, {
  "name": "Clamp Entities to Ground",
  "isNew": false,
  "img": "Clamp Entities to Ground.jpg"
}, {
  "name": "Clamp Model to Ground",
  "isNew": false,
  "img": "Clamp Model to Ground.jpg"
}, {
  "name": "Clamp to 3D Model",
  "isNew": false,
  "img": "Clamp to 3D Model.jpg"
}, {
  "name": "Classification",
  "isNew": false,
  "img": "Classification.jpg"
}, {
  "name": "Classification Types",
  "isNew": false,
  "img": "Classification Types.jpg"
}, {
  "name": "Clipping Regions",
  "isNew": false,
  "img": "Clipping Regions.jpg"
}, {
  "name": "Clock",
  "isNew": false,
  "img": "Clock.jpg"
}, {
  "name": "Cloud Parameters",
  "isNew": false,
  "img": "Cloud Parameters.jpg"
}, {
  "name": "Clouds",
  "isNew": false,
  "img": "Clouds.jpg"
}, {
  "name": "Clustering",
  "isNew": false,
  "img": "Clustering.jpg"
}, {
  "name": "Corridor",
  "isNew": false,
  "img": "Corridor.jpg"
}, {
  "name": "Custom DataSource",
  "isNew": false,
  "img": "Custom DataSource.jpg"
}, {
  "name": "Custom Geocoder",
  "isNew": false,
  "img": "Custom Geocoder.jpg"
}, {
  "name": "Custom Per-Feature Post Process",
  "isNew": false,
  "img": "Custom Per-Feature Post Process.jpg"
}, {
  "name": "Custom Post Process",
  "isNew": false,
  "img": "Custom Post Process.jpg"
}, {
  "name": "Custom Shaders 3D Tiles",
  "isNew": false,
  "img": "Custom Shaders 3D Tiles.jpg"
}, {
  "name": "Custom Shaders Models",
  "isNew": false,
  "img": "Custom Shaders Models.jpg"
}, {
  "name": "Custom Shaders Property Textures",
  "isNew": false,
  "img": "Custom Shaders Property Textures.jpg"
}, {
  "name": "Cylinders and Cones",
  "isNew": false,
  "img": "Cylinders and Cones.jpg"
}, {
  "name": "DataSource Ordering",
  "isNew": false,
  "img": "DataSource Ordering.jpg"
}, {
  "name": "Depth of Field",
  "isNew": false,
  "img": "Depth of Field.jpg"
}, {
  "name": "Distance Display Conditions",
  "isNew": false,
  "img": "Distance Display Conditions.jpg"
}, {
  "name": "Drape Imagery on 3D Tiles",
  "isNew": true,
  "img": "Drape Imagery on 3D Tiles.jpg"
}, {
  "name": "Drawing on Terrain",
  "isNew": false,
  "img": "Drawing on Terrain.jpg"
}, {
  "name": "Earth at Night",
  "isNew": false,
  "img": "Earth at Night.jpg"
}, {
  "name": "Elevation Band Material",
  "isNew": false,
  "img": "Elevation Band Material.jpg"
}, {
  "name": "Entity tracking",
  "isNew": false,
  "img": "Entity tracking.jpg"
}, {
  "name": "Export KML",
  "isNew": false,
  "img": "Export KML.jpg"
}, {
  "name": "FXAA",
  "isNew": false,
  "img": "FXAA.jpg"
}, {
  "name": "Fog",
  "isNew": false,
  "img": "Fog.jpg"
}, {
  "name": "Fog Post Process",
  "isNew": false,
  "img": "Fog Post Process.jpg"
}, {
  "name": "GPX",
  "isNew": false,
  "img": "GPX.jpg"
}, {
  "name": "GeoJSON and TopoJSON",
  "isNew": false,
  "img": "GeoJSON and TopoJSON.jpg"
}, {
  "name": "GeoJSON simplestyle",
  "isNew": false,
  "img": "GeoJSON simplestyle.jpg"
}, {
  "name": "Geometry Height Reference",
  "isNew": false,
  "img": "Geometry Height Reference.jpg"
}, {
  "name": "Geometry and Appearances",
  "isNew": false,
  "img": "Geometry and Appearances.jpg"
}, {
  "name": "Globe Interior",
  "isNew": false,
  "img": "Globe Interior.jpg"
}, {
  "name": "Globe Materials",
  "isNew": false,
  "img": "Globe Materials.jpg"
}, {
  "name": "Globe Materials – Water Mask Elevation Map",
  "isNew": false,
  "img": "Globe Materials – Water Mask Elevation Map.jpg"
}, {
  "name": "Globe Translucency",
  "isNew": false,
  "img": "Globe Translucency.jpg"
}, {
  "name": "Google Earth Enterprise",
  "isNew": false,
  "img": "Google Earth Enterprise.jpg"
}, {
  "name": "Google Photorealistic 3D Tiles",
  "isNew": false,
  "img": "Google Photorealistic 3D Tiles.jpg"
}, {
  "name": "Google Photorealistic 3D Tiles with Building Insert",
  "isNew": false,
  "img": "Google Photorealistic 3D Tiles with Building Insert.jpg"
}, {
  "name": "HTML Overlays",
  "isNew": false,
  "img": "HTML Overlays.jpg"
}, {
  "name": "HeadingPitchRoll",
  "isNew": false,
  "img": "HeadingPitchRoll.jpg"
}, {
  "name": "Hello World",
  "isNew": false,
  "img": "Hello World.jpg"
}, {
  "name": "High Dynamic Range",
  "isNew": false,
  "img": "High Dynamic Range.jpg"
}, {
  "name": "I3S 3D Object Layer",
  "isNew": false,
  "img": "I3S 3D Object Layer.jpg"
}, {
  "name": "I3S Building Scene Layer",
  "isNew": false,
  "img": "I3S Building Scene Layer.jpg"
}, {
  "name": "I3S Feature Picking",
  "isNew": false,
  "img": "I3S Feature Picking.jpg"
}, {
  "name": "I3S IntegratedMesh Layer",
  "isNew": false,
  "img": "I3S IntegratedMesh Layer.jpg"
}, {
  "name": "Image-Based Lighting",
  "isNew": false,
  "img": "Image-Based Lighting.jpg"
}, {
  "name": "Imagery Adjustment",
  "isNew": false,
  "img": "Imagery Adjustment.jpg"
}, {
  "name": "Imagery Color To Alpha",
  "isNew": false,
  "img": "Imagery Color To Alpha.jpg"
}, {
  "name": "Imagery Cutout",
  "isNew": false,
  "img": "Imagery Cutout.jpg"
}, {
  "name": "Imagery Layers",
  "isNew": false,
  "img": "Imagery Layers.jpg"
}, {
  "name": "Imagery Layers Manipulation",
  "isNew": false,
  "img": "Imagery Layers Manipulation.jpg"
}, {
  "name": "Imagery Layers Split",
  "isNew": false,
  "img": "Imagery Layers Split.jpg"
}, {
  "name": "Imagery Layers Texture Filters",
  "isNew": false,
  "img": "Imagery Layers Texture Filters.jpg"
}, {
  "name": "Interpolation",
  "isNew": false,
  "img": "Interpolation.jpg"
}, {
  "name": "Japan Buildings",
  "isNew": false,
  "img": "Japan Buildings.jpg"
}, {
  "name": "KML",
  "isNew": false,
  "img": "KML.jpg"
}, {
  "name": "KML Tours",
  "isNew": false,
  "img": "KML Tours.jpg"
}, {
  "name": "Labels",
  "isNew": false,
  "img": "Labels.jpg"
}, {
  "name": "LensFlare",
  "isNew": false,
  "img": "LensFlare.jpg"
}, {
  "name": "Lighting",
  "isNew": false,
  "img": "Lighting.jpg"
}, {
  "name": "LocalToFixedFrame",
  "isNew": false,
  "img": "LocalToFixedFrame.jpg"
}, {
  "name": "MSAA",
  "isNew": false,
  "img": "MSAA.jpg"
}, {
  "name": "Manually Controlled Animation",
  "isNew": false,
  "img": "Manually Controlled Animation.jpg"
}, {
  "name": "Map Pins",
  "isNew": false,
  "img": "Map Pins.jpg"
}, {
  "name": "Material with Custom GLSL",
  "isNew": false,
  "img": "Material with Custom GLSL.jpg"
}, {
  "name": "Materials",
  "isNew": false,
  "img": "Materials.jpg"
}, {
  "name": "Montreal Point Cloud",
  "isNew": false,
  "img": "Montreal Point Cloud.jpg"
}, {
  "name": "Moon",
  "isNew": false,
  "img": "Moon.jpg"
}, {
  "name": "Multi-part CZML",
  "isNew": false,
  "img": "Multi-part CZML.jpg"
}, {
  "name": "Multiple Synced Views",
  "isNew": false,
  "img": "Multiple Synced Views.jpg"
}, {
  "name": "Natural Earth II",
  "isNew": false,
  "img": "Natural Earth II.jpg"
}, {
  "name": "Offline",
  "isNew": false,
  "img": "Offline.jpg"
}, {
  "name": "PAMAP Terrain",
  "isNew": false,
  "img": "PAMAP Terrain.jpg"
}, {
  "name": "Parallels and Meridians",
  "isNew": false,
  "img": "Parallels and Meridians.jpg"
}, {
  "name": "Partial Ellipsoids",
  "isNew": false,
  "img": "Partial Ellipsoids.jpg"
}, {
  "name": "Particle System",
  "isNew": false,
  "img": "Particle System.jpg"
}, {
  "name": "Particle System Fireworks",
  "isNew": false,
  "img": "Particle System Fireworks.jpg"
}, {
  "name": "Particle System Tails",
  "isNew": false,
  "img": "Particle System Tails.jpg"
}, {
  "name": "Particle System Weather",
  "isNew": false,
  "img": "Particle System Weather.jpg"
}, {
  "name": "Per-Feature Post Processing",
  "isNew": false,
  "img": "Per-Feature Post Processing.jpg"
}, {
  "name": "Physically-Based Materials",
  "isNew": false,
  "img": "Physically-Based Materials.jpg"
}, {
  "name": "Picking",
  "isNew": false,
  "img": "Picking.jpg"
}, {
  "name": "Plane",
  "isNew": false,
  "img": "Plane.jpg"
}, {
  "name": "Points",
  "isNew": false,
  "img": "Points.jpg"
}, {
  "name": "Polygon",
  "isNew": false,
  "img": "Polygon.jpg"
}, {
  "name": "Polyline",
  "isNew": false,
  "img": "Polyline.jpg"
}, {
  "name": "Polyline Dash",
  "isNew": false,
  "img": "Polyline Dash.jpg"
}, {
  "name": "Polyline Volume",
  "isNew": false,
  "img": "Polyline Volume.jpg"
}, {
  "name": "Polylines on 3D Tiles",
  "isNew": false,
  "img": "Polylines on 3D Tiles.jpg"
}, {
  "name": "Post Processing",
  "isNew": false,
  "img": "Post Processing.jpg"
}, {
  "name": "Procedural Terrain",
  "isNew": false,
  "img": "Procedural Terrain.jpg"
}, {
  "name": "Projection",
  "isNew": false,
  "img": "Projection.jpg"
}, {
  "name": "Rectangle",
  "isNew": false,
  "img": "Rectangle.jpg"
}, {
  "name": "Resolution Scaling",
  "isNew": false,
  "img": "Resolution Scaling.jpg"
}, {
  "name": "Rotatable 2D Map",
  "isNew": false,
  "img": "Rotatable 2D Map.jpg"
}, {
  "name": "Sample Height from 3D Tiles",
  "isNew": false,
  "img": "Sample Height from 3D Tiles.jpg"
}, {
  "name": "Scene Rendering Performance",
  "isNew": false,
  "img": "Scene Rendering Performance.jpg"
}, {
  "name": "Sentinel-2",
  "isNew": false,
  "img": "Sentinel-2.jpg"
}, {
  "name": "Shadows",
  "isNew": false,
  "img": "Shadows.jpg"
}, {
  "name": "Show or Hide Entities",
  "isNew": false,
  "img": "Show or Hide Entities.jpg"
}, {
  "name": "Spheres and Ellipsoids",
  "isNew": false,
  "img": "Spheres and Ellipsoids.jpg"
}, {
  "name": "SplitDirection",
  "isNew": false,
  "img": "SplitDirection.jpg"
}, {
  "name": "Star Burst",
  "isNew": false,
  "img": "Star Burst.jpg"
}, {
  "name": "Terrain",
  "isNew": false,
  "img": "Terrain.jpg"
}, {
  "name": "Terrain Clipping Planes",
  "isNew": false,
  "img": "Terrain Clipping Planes.jpg"
}, {
  "name": "Terrain Exaggeration",
  "isNew": false,
  "img": "Terrain Exaggeration.jpg"
}, {
  "name": "Time Dynamic Point Cloud",
  "isNew": false,
  "img": "Time Dynamic Point Cloud.jpg"
}, {
  "name": "Time Dynamic Wheels",
  "isNew": false,
  "img": "Time Dynamic Wheels.jpg"
}, {
  "name": "Underground Color",
  "isNew": false,
  "img": "Underground Color.jpg"
}, {
  "name": "Video",
  "isNew": false,
  "img": "Video.jpg"
}, {
  "name": "Voxel Picking",
  "isNew": false,
  "img": "Voxel Picking.jpg"
}, {
  "name": "Voxels",
  "isNew": false,
  "img": "Voxels.jpg"
}, {
  "name": "Voxels in 3D Tiles",
  "isNew": false,
  "img": "Voxels in 3D Tiles.jpg"
}, {
  "name": "Wall",
  "isNew": false,
  "img": "Wall.jpg"
}, {
  "name": "Washington DC 2017",
  "isNew": false,
  "img": "Washington DC 2017.jpg"
}, {
  "name": "Web Map Service (WMS)",
  "isNew": false,
  "img": "Web Map Service (WMS).jpg"
}, {
  "name": "Web Map Tile Service with Time",
  "isNew": false,
  "img": "Web Map Tile Service with Time.jpg"
}, {
  "name": "Z-Indexing Geometry",
  "isNew": false,
  "img": "Z-Indexing Geometry.jpg"
}, {
  "name": "glTF PBR Extensions",
  "isNew": false,
  "img": "glTF PBR Extensions.jpg"
}, {
  "name": "iModel Mesh Export Service",
  "isNew": false,
  "img": "iModel Mesh Export Service.jpg"
}, {
  "name": "iTwin Feature Service",
  "isNew": false,
  "img": "iTwin Feature Service.jpg"
}];
const has_new_gallery_demos = true;
