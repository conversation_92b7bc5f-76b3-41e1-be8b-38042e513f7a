<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Add imagery from a Web Map Service (WMS) server." />
    <meta name="cesium-sandcastle-labels" content="Beginner" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          baseLayerPicker: false,
        });

        try {
          const geeMetadata = await Cesium.GoogleEarthEnterpriseMetadata.fromUrl(
            new Cesium.Resource({
              url: "http://www.earthenterprise.org/3d",
              proxy: new Cesium.DefaultProxy("/proxy/"),
            }),
          );

          viewer.scene.terrainProvider =
            Cesium.GoogleEarthEnterpriseTerrainProvider.fromMetadata(geeMetadata);

          const layers = viewer.scene.imageryLayers;
          const blackMarble = new Cesium.ImageryLayer(
            new Cesium.GoogleEarthEnterpriseImageryProvider({
              metadata: geeMetadata,
            }),
          );
          layers.add(blackMarble);
        } catch (error) {
          console.log(`Failed to create Google Earth providers from metadata. Confirm GEE service is correctly configured.
          ${error}`);
        }

        // Start off looking at San Francisco.
        viewer.camera.setView({
          destination: Cesium.Rectangle.fromDegrees(-123.0, 36.0, -121.7, 39.0),
        }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
