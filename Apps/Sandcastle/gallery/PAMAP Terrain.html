<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="High resolution terrain of Pennsylvania curated by PASDA and hosted on Cesium ion."
    />
    <meta name="cesium-sandcastle-labels" content="ion Assets" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        try {
          // High resolution terrain of Pennsylvania curated by Pennsylvania Spatial Data Access (PASDA)
          // http://www.pasda.psu.edu/
          viewer.terrainProvider =
            await Cesium.CesiumTerrainProvider.fromIonAssetId(3957);
        } catch (error) {
          window.alert(`Failed to load terrain. ${error}`);
        }

        // Add PA locations
        Sandcastle.addDefaultToolbarMenu(
          [
            {
              text: "Pinnacle",
              onselect: function () {
                viewer.scene.camera.flyTo({
                  destination: Cesium.Cartesian3.fromRadians(
                    -1.3324415110874286,
                    0.6954224325279967,
                    236.6770689945084,
                  ),
                  orientation: {
                    heading: Cesium.Math.toRadians(310),
                    pitch: Cesium.Math.toRadians(-15),
                    roll: 0.0,
                  },
                });
              },
            },
            {
              text: "Mount Nittany",
              onselect: function () {
                viewer.scene.camera.flyTo({
                  destination: Cesium.Cartesian3.fromRadians(
                    -1.358985133937573,
                    0.7123252393978314,
                    451.05748252867375,
                  ),
                  orientation: {
                    heading: Cesium.Math.toRadians(85),
                    pitch: Cesium.Math.toRadians(0),
                    roll: 0.0,
                  },
                });
              },
            },
            {
              text: "Horseshoe Curve",
              onselect: function () {
                viewer.scene.camera.flyTo({
                  destination: Cesium.Cartesian3.fromRadians(
                    -1.3700147546199826,
                    0.706808606166025,
                    993.7916313325215,
                  ),
                  orientation: {
                    heading: Cesium.Math.toRadians(90),
                    pitch: Cesium.Math.toRadians(-15),
                    roll: 0.0,
                  },
                });
              },
            },
            {
              text: "Jim Thorpe",
              onselect: function () {
                viewer.scene.camera.flyTo({
                  destination: Cesium.Cartesian3.fromRadians(
                    -1.3218297501066052,
                    0.713358272291525,
                    240.87968743408845,
                  ),
                  orientation: {
                    heading: Cesium.Math.toRadians(200),
                    pitch: Cesium.Math.toRadians(-5),
                    roll: 0.0,
                  },
                });
              },
            },
            {
              text: "Grand Canyon of PA",
              onselect: function () {
                viewer.scene.camera.flyTo({
                  destination: Cesium.Cartesian3.fromRadians(
                    -1.349379633251472,
                    0.720297672225785,
                    656.268309953562,
                  ),
                  orientation: {
                    heading: Cesium.Math.toRadians(200),
                    pitch: Cesium.Math.toRadians(-5),
                    roll: 0.0,
                  },
                });
              },
            },
          ],
          "toolbar",
        );

        viewer.scene.camera.flyTo({
          destination: Cesium.Cartesian3.fromRadians(
            -1.3324415110874286,
            0.6954224325279967,
            236.6770689945084,
          ),
          orientation: {
            heading: Cesium.Math.toRadians(310),
            pitch: Cesium.Math.toRadians(-15),
            roll: 0.0,
          },
          duration: 0.0,
        });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
