<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Z-indexing" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML zIndex",
            version: "1.0",
          },
          {
            id: "shape1",
            name: "Blue circle",
            position: {
              cartographicDegrees: [-105.0, 40.0, 0.0],
            },
            ellipse: {
              semiMinorAxis: 300000.0,
              semiMajorAxis: 300000.0,
              zIndex: 3,
              material: {
                solidColor: {
                  color: {
                    rgba: [0, 0, 255, 255],
                  },
                },
              },
            },
          },
          {
            id: "shape2",
            name: "Green corridor",
            corridor: {
              positions: {
                cartographicDegrees: [-90.0, 43.0, 0, -95.0, 43.0, 0, -95.0, 38.0, 0],
              },
              width: 200000.0,
              zIndex: 3,
              material: {
                solidColor: {
                  color: {
                    rgba: [0, 255, 0, 255],
                  },
                },
              },
            },
          },
          {
            id: "shape3",
            name: "Red polygon",
            polygon: {
              positions: {
                cartographicDegrees: [
                  -115.0, 47.0, 0, -115.0, 42.0, 0, -107.0, 43.0, 0, -102.0, 41.0, 0,
                  -102.0, 45.0, 0,
                ],
              },
              zIndex: 1,
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 0, 0, 255],
                  },
                },
              },
            },
          },
          {
            id: "shape4",
            name: "Striped rectangle",
            rectangle: {
              coordinates: {
                wsenDegrees: [-105, 40, -95, 50],
              },
              zIndex: 2,
              fill: true,
              material: {
                stripe: {
                  orientation: "VERTICAL",
                  evenColor: { rgba: [255, 255, 0, 255] },
                  oddColor: { rgba: [255, 0, 255, 255] },
                  repeat: 5,
                },
              },
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer");
        const dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        viewer.zoomTo(dataSourcePromise);

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
