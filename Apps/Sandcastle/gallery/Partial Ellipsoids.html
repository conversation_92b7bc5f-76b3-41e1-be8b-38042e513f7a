<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw whole/partial spheres and ellipsoids." />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const saturnPosition = Cesium.Cartesian3.fromDegrees(-95.0, 45.0, 300000.0);
        viewer.entities.add({
          name: "Saturn",
          position: saturnPosition,
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            material: new Cesium.Color(0.95, 0.82, 0.49),
          },
        });

        viewer.entities.add({
          name: "Saturn's inner ring",
          position: saturnPosition,
          orientation: Cesium.Transforms.headingPitchRollQuaternion(
            saturnPosition,
            new Cesium.HeadingPitchRoll(
              Cesium.Math.toRadians(30.0),
              Cesium.Math.toRadians(30.0),
              0.0,
            ),
          ),
          ellipsoid: {
            radii: new Cesium.Cartesian3(400000.0, 400000.0, 400000.0),
            innerRadii: new Cesium.Cartesian3(300000.0, 300000.0, 300000.0),
            minimumCone: Cesium.Math.toRadians(89.8),
            maximumCone: Cesium.Math.toRadians(90.2),
            material: new Cesium.Color(0.95, 0.82, 0.49, 0.5),
          },
        });

        viewer.entities.add({
          name: "Saturn's outer ring",
          position: saturnPosition,
          orientation: Cesium.Transforms.headingPitchRollQuaternion(
            saturnPosition,
            new Cesium.HeadingPitchRoll(
              Cesium.Math.toRadians(30.0),
              Cesium.Math.toRadians(30.0),
              0.0,
            ),
          ),
          ellipsoid: {
            radii: new Cesium.Cartesian3(460000.0, 460000.0, 460000.0),
            innerRadii: new Cesium.Cartesian3(415000.0, 415000.0, 415000.0),
            minimumCone: Cesium.Math.toRadians(89.8),
            maximumCone: Cesium.Math.toRadians(90.2),
            material: new Cesium.Color(0.95, 0.82, 0.49, 0.5),
          },
        });

        viewer.entities.add({
          name: "Dome",
          position: Cesium.Cartesian3.fromDegrees(-120.0, 40.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            maximumCone: Cesium.Math.PI_OVER_TWO,
            material: Cesium.Color.BLUE.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Dome with inner radius",
          position: Cesium.Cartesian3.fromDegrees(-114.0, 40.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(250000.0, 200000.0, 150000.0),
            innerRadii: new Cesium.Cartesian3(100000.0, 80000.0, 60000.0),
            maximumCone: Cesium.Math.PI_OVER_TWO,
            material: Cesium.Color.RED.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Dome with top cut out",
          position: Cesium.Cartesian3.fromDegrees(-108.0, 40.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            innerRadii: new Cesium.Cartesian3(100000.0, 100000.0, 100000.0),
            minimumCone: Cesium.Math.toRadians(20.0),
            maximumCone: Cesium.Math.PI_OVER_TWO,
            material: Cesium.Color.YELLOW.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Top and bottom cut out",
          position: Cesium.Cartesian3.fromDegrees(-102.0, 40.0, 140000.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            innerRadii: new Cesium.Cartesian3(100000.0, 100000.0, 100000.0),
            minimumCone: Cesium.Math.toRadians(60.0),
            maximumCone: Cesium.Math.toRadians(140.0),
            material: Cesium.Color.DARKCYAN.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Bowl",
          position: Cesium.Cartesian3.fromDegrees(-96.0, 39.5, 200000.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            innerRadii: new Cesium.Cartesian3(180000.0, 180000.0, 180000.0),
            minimumCone: Cesium.Math.toRadians(110.0),
            material: Cesium.Color.GREEN.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Clock cutout",
          position: Cesium.Cartesian3.fromDegrees(-90.0, 39.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            innerRadii: new Cesium.Cartesian3(150000.0, 150000.0, 150000.0),
            minimumClock: Cesium.Math.toRadians(-90.0),
            maximumClock: Cesium.Math.toRadians(180.0),
            minimumCone: Cesium.Math.toRadians(20.0),
            maximumCone: Cesium.Math.toRadians(70.0),
            material: Cesium.Color.BLUE.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Partial dome",
          position: Cesium.Cartesian3.fromDegrees(-84.0, 38.5),
          ellipsoid: {
            radii: new Cesium.Cartesian3(200000.0, 200000.0, 200000.0),
            minimumClock: Cesium.Math.toRadians(-90.0),
            maximumClock: Cesium.Math.toRadians(180.0),
            maximumCone: Cesium.Math.toRadians(90.0),
            material: Cesium.Color.RED.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.entities.add({
          name: "Wedge",
          position: Cesium.Cartesian3.fromDegrees(-102.0, 35.0, 20000.0),
          orientation: Cesium.Transforms.headingPitchRollQuaternion(
            Cesium.Cartesian3.fromDegrees(-102.0, 35.0, 20000.0),
            new Cesium.HeadingPitchRoll(Cesium.Math.PI / 1.5, 0, 0.0),
          ),
          ellipsoid: {
            radii: new Cesium.Cartesian3(500000.0, 500000.0, 500000.0),
            innerRadii: new Cesium.Cartesian3(10000.0, 10000.0, 10000.0),
            minimumClock: Cesium.Math.toRadians(-15.0),
            maximumClock: Cesium.Math.toRadians(15.0),
            minimumCone: Cesium.Math.toRadians(75.0),
            maximumCone: Cesium.Math.toRadians(105.0),
            material: Cesium.Color.DARKCYAN.withAlpha(0.3),
            outline: true,
          },
        });
        viewer.entities.add({
          name: "Partial ellipsoid",
          position: Cesium.Cartesian3.fromDegrees(-95.0, 34.0),
          ellipsoid: {
            radii: new Cesium.Cartesian3(300000.0, 300000.0, 300000.0),
            innerRadii: new Cesium.Cartesian3(70000.0, 70000.0, 70000.0),
            minimumClock: Cesium.Math.toRadians(180.0),
            maximumClock: Cesium.Math.toRadians(400.0),
            maximumCone: Cesium.Math.toRadians(90.0),
            material: Cesium.Color.DARKCYAN.withAlpha(0.3),
            outline: true,
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
