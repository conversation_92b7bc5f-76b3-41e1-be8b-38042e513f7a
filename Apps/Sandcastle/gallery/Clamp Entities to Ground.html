<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Clamp entities to terrain and 3D Tiles" />
    <meta
      name="cesium-sandcastle-labels"
      content="Showcases, 3D Tiles, Terrain, Entity"
    />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <div id="terrainMenu"></div>
      <div id="zoomButtons"></div>
      <div id="toggleLighting"></div>
      <div id="sampleButtons"></div>
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          timeline: false,
          animation: false,
          baseLayerPicker: false,
          geocoder: Cesium.IonGeocodeProviderType.GOOGLE,
        });
        const scene = viewer.scene;
        scene.globe.depthTestAgainstTerrain = true;

        let worldTerrain;
        try {
          worldTerrain = await Cesium.createWorldTerrainAsync();
          viewer.scene.terrainProvider = worldTerrain;
          scene.globe.show = false;
        } catch (error) {
          window.alert(`There was an error creating world terrain. ${error}`);
        }

        let worldTileset;
        try {
          worldTileset = await Cesium.createGooglePhotorealistic3DTileset({
            // Only the Google Geocoder can be used with Google Photorealistic 3D Tiles.  Set the `geocode` property of the viewer constructor options to IonGeocodeProviderType.GOOGLE.
            onlyUsingWithGoogleGeocoder: true,
          });
          viewer.scene.primitives.add(worldTileset);
        } catch (error) {
          console.log(`Error loading Photorealistic 3D Tiles tileset.
            ${error}`);
        }

        Sandcastle.addToolbarMenu([
          {
            text: "3D Tiles",
            onselect: () => {
              scene.globe.show = false;
              worldTileset.show = true;
            },
          },
          {
            text: "Terrain",
            onselect: () => {
              scene.globe.show = true;
              worldTileset.show = false;
            },
          },
        ]);

        Sandcastle.addDefaultToolbarMenu([
          {
            //
            // To clamp points or billboards set the heightReference to CLAMP_TO_GROUND or RELATIVE_TO_GROUND
            //
            text: "Draw Point with Label",
            onselect: function () {
              viewer.entities.removeAll();
              const pointAndLabel = viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(-122.1965, 46.1915),
                point: {
                  color: Cesium.Color.CORNFLOWERBLUE,
                  pixelSize: 18,
                  outlineColor: Cesium.Color.DARKSLATEGREY,
                  outlineWidth: 3,
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                  disableDepthTestDistance: Number.POSITIVE_INFINITY,
                },
                label: {
                  text: "Clamped to ground",
                  font: "14pt sans-serif",
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                  horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                  verticalOrigin: Cesium.VerticalOrigin.BASELINE,
                  fillColor: Cesium.Color.GHOSTWHITE,
                  showBackground: true,
                  backgroundColor: Cesium.Color.DARKSLATEGREY.withAlpha(0.8),
                  backgroundPadding: new Cesium.Cartesian2(8, 4),
                  pixelOffset: new Cesium.Cartesian2(15, 6),
                  disableDepthTestDistance: Number.POSITIVE_INFINITY,
                },
              });
              viewer.trackedEntity = pointAndLabel;
            },
          },
          {
            text: "Draw Billboard",
            onselect: function () {
              viewer.entities.removeAll();
              const e = viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(-122.1958, 46.1915),
                billboard: {
                  image: "../images/facility.gif",
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
              });

              viewer.trackedEntity = e;
            },
          },
          {
            //
            // Corridors, polygons and rectangles will be clamped automatically if they are filled with a constant color and
            // has no height or extruded height.
            // NOTE: Setting height to 0 will disable clamping.
            //
            text: "Draw Corridor",
            onselect: function () {
              viewer.entities.removeAll();
              const e = viewer.entities.add({
                corridor: {
                  positions: Cesium.Cartesian3.fromDegreesArray([
                    -122.19, 46.1914, -122.21, 46.21, -122.23, 46.21,
                  ]),
                  width: 2000.0,
                  material: Cesium.Color.GREEN.withAlpha(0.5),
                },
              });

              viewer.zoomTo(e);
            },
          },
          {
            text: "Draw Polygon",
            onselect: function () {
              viewer.entities.removeAll();
              const e = viewer.entities.add({
                polygon: {
                  hierarchy: {
                    positions: [
                      new Cesium.Cartesian3(
                        -2358138.847340281,
                        -3744072.459541374,
                        4581158.5714175375,
                      ),
                      new Cesium.Cartesian3(
                        -2357231.4925370603,
                        -3745103.7886602185,
                        4580702.9757762635,
                      ),
                      new Cesium.Cartesian3(
                        -2355912.902205431,
                        -3744249.029778454,
                        4582402.154378103,
                      ),
                      new Cesium.Cartesian3(
                        -2357208.0209552636,
                        -3743553.4420488174,
                        4581961.863286629,
                      ),
                    ],
                  },
                  material: Cesium.Color.BLUE.withAlpha(0.5),
                },
              });

              viewer.zoomTo(e);
            },
          },
          {
            text: "Draw Textured Polygon",
            onselect: function () {
              if (!Cesium.Entity.supportsMaterialsforEntitiesOnTerrain(viewer.scene)) {
                window.alert(
                  "Terrain Entity materials are not supported on this platform",
                );
                return;
              }

              viewer.entities.removeAll();
              const e = viewer.entities.add({
                polygon: {
                  hierarchy: {
                    positions: [
                      new Cesium.Cartesian3(
                        -2358138.847340281,
                        -3744072.459541374,
                        4581158.5714175375,
                      ),
                      new Cesium.Cartesian3(
                        -2357231.4925370603,
                        -3745103.7886602185,
                        4580702.9757762635,
                      ),
                      new Cesium.Cartesian3(
                        -2355912.902205431,
                        -3744249.029778454,
                        4582402.154378103,
                      ),
                      new Cesium.Cartesian3(
                        -2357208.0209552636,
                        -3743553.4420488174,
                        4581961.863286629,
                      ),
                    ],
                  },
                  material: "../images/Cesium_Logo_Color.jpg",
                  classificationType: Cesium.ClassificationType.BOTH,
                  stRotation: Cesium.Math.toRadians(45),
                },
              });

              viewer.zoomTo(e);
            },
          },
          {
            text: "Draw Rectangle",
            onselect: function () {
              viewer.entities.removeAll();
              const e = viewer.entities.add({
                rectangle: {
                  coordinates: Cesium.Rectangle.fromDegrees(-122.3, 46.0, -122.0, 46.3),
                  material: Cesium.Color.RED.withAlpha(0.5),
                },
              });

              viewer.zoomTo(e);
            },
          },
          {
            text: "Draw Model",
            onselect: function () {
              viewer.entities.removeAll();
              const e = viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(-122.1958, 46.1915),
                model: {
                  uri: "../../SampleData/models/CesiumMan/Cesium_Man.glb",
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                  minimumPixelSize: 128,
                  maximumScale: 100,
                },
              });

              viewer.trackedEntity = e;
            },
          },
          {
            text: "Draw polyline",
            onselect: function () {
              if (!Cesium.Entity.supportsPolylinesOnTerrain(viewer.scene)) {
                window.alert("Polylines on terrain are not supported on this platform");
              }

              viewer.entities.removeAll();
              viewer.entities.add({
                polyline: {
                  positions: Cesium.Cartesian3.fromDegreesArray([
                    86.953793, 27.928257, 86.953793, 27.988257, 86.896497, 27.988257,
                  ]),
                  clampToGround: true,
                  width: 5,
                  material: new Cesium.PolylineOutlineMaterialProperty({
                    color: Cesium.Color.ORANGE,
                    outlineWidth: 2,
                    outlineColor: Cesium.Color.BLACK,
                  }),
                },
              });

              const target = new Cesium.Cartesian3(
                300770.50872389384,
                5634912.131394585,
                2978152.2865545116,
              );
              const offset = new Cesium.Cartesian3(
                6344.974098678562,
                -793.3419798081741,
                2499.9508860763162,
              );
              viewer.camera.lookAt(target, offset);
              viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
            },
          },
        ]);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
