<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Configure the Viewer clock." />
    <meta name="cesium-sandcastle-labels" content="Beginner" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create a clock that loops on Christmas day 2013 and runs in 4000x real time.
        const clock = new Cesium.Clock({
          startTime: Cesium.JulianDate.fromIso8601("2013-12-25"),
          currentTime: Cesium.JulianDate.fromIso8601("2013-12-25"),
          stopTime: Cesium.JulianDate.fromIso8601("2013-12-26"),
          clockRange: Cesium.ClockRange.LOOP_STOP, // loop when we hit the end time
          clockStep: Cesium.ClockStep.SYSTEM_CLOCK_MULTIPLIER,
          multiplier: 4000, // how much time to advance each tick
          shouldAnimate: true, // Animation on by default
        });

        const viewer = new Cesium.Viewer("cesiumContainer", {
          clockViewModel: new Cesium.ClockViewModel(clock),
        });

        viewer.scene.globe.enableLighting = true;

        Sandcastle.addToolbarButton("Reset Current Time", function () {
          const resetTime = viewer.clockViewModel.startTime;
          viewer.clockViewModel.currentTime = resetTime;
          viewer.timeline.updateFromClock();
        });

        Sandcastle.addToolbarButton("Slow Down Clock", function () {
          viewer.clockViewModel.multiplier /= 2;
        });

        Sandcastle.addToolbarButton("Speed Up Clock", function () {
          viewer.clockViewModel.multiplier *= 2;
        }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
      }
    </script>
  </body>
</html>
