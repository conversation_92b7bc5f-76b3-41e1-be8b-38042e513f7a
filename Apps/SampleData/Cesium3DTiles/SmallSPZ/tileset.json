{"asset": {"version": "1.1"}, "extensions": {"3DTILES_content_gltf": {"extensionsRequired": ["KHR_spz_gaussian_splats_compression"], "extensionsUsed": ["KHR_spz_gaussian_splats_compression"]}}, "extensionsUsed": ["3DTILES_content_gltf"], "geometricError": 1000, "root": {"boundingVolume": {"box": [0, 0, 0, 10, 0, 0, 0, 10, 0, 0, 0, 10]}, "geometricError": 500, "refine": "REPLACE", "children": [{"boundingVolume": {"box": [0, 0, 0, 10, 0, 0, 0, 10, 0, 0, 0, 10]}, "content": {"uri": "../Demo/content.glb"}, "geometricError": 0, "refine": "REPLACE"}]}}