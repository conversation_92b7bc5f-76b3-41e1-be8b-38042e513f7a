{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "children": [3, 1], "matrix": [0, 0, 1, 0, 0, 1, 0, 0, -1, 0, 0, 0, 0, 0, 0, 1]}, {"children": [2], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -1.352329969406128, 0.4277220070362091, -2.98022992950564e-08, 1]}, {"mesh": 1, "rotation": [0, 0, 0.08848590403795242, -0.9960774183273317]}, {"children": [4], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1.432669997215271, 0.4277220070362091, -2.98022992950564e-08, 1]}, {"mesh": 1, "rotation": [0, 0, 0.08848590403795242, -0.9960774183273317]}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 5, "POSITION": 6, "TEXCOORD_0": 7}, "indices": 4, "mode": 4, "material": 0, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 2, "attributes": {"NORMAL": 0, "POSITION": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"NORMAL": 9, "POSITION": 10}, "indices": 8, "mode": 4, "material": 1, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 3, "attributes": {"NORMAL": 0, "POSITION": 1}}}}, {"attributes": {"NORMAL": 12, "POSITION": 13}, "indices": 11, "mode": 4, "material": 2, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 4, "attributes": {"NORMAL": 0, "POSITION": 1}}}}], "name": "Cesium_Milk_Truck"}, {"primitives": [{"attributes": {"NORMAL": 15, "POSITION": 16, "TEXCOORD_0": 17}, "indices": 14, "mode": 4, "material": 3, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 5, "attributes": {"NORMAL": 0, "POSITION": 1, "TEXCOORD_0": 2}}}}], "name": "Wheels"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 4, "path": "rotation"}}, {"sampler": 1, "target": {"node": 2, "path": "rotation"}}], "samplers": [{"input": 0, "interpolation": "LINEAR", "output": 1}, {"input": 2, "interpolation": "LINEAR", "output": 3}]}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5126, "count": 31, "max": [1.25], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 31, "max": [0, 0, 0.9990190863609314, 1], "min": [0, 0, 0, -0.9960774183273317], "type": "VEC4"}, {"bufferView": 0, "byteOffset": 124, "componentType": 5126, "count": 31, "max": [1.25], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 496, "componentType": 5126, "count": 31, "max": [0, 0, 0.9990190863609314, 1], "min": [0, 0, 0, -0.9960774183273317], "type": "VEC4"}, {"componentType": 5123, "count": 5232, "max": [1855], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 1856, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"componentType": 5126, "count": 1856, "max": [2.437999963760376, 2.5843698978424072, 1.3960000276565552], "min": [-2.430910110473633, 0.2667999863624573, -1.3960000276565552], "type": "VEC3"}, {"componentType": 5126, "count": 1856, "max": [0.8964580297470093, 0.997245192527771], "min": [0.002956389915198088, 0.015672028064727783], "type": "VEC2"}, {"componentType": 5123, "count": 168, "max": [71], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 72, "max": [0.957480013370514, 0.28850099444389343, 1], "min": [-1, 0, -1], "type": "VEC3"}, {"componentType": 5126, "count": 72, "max": [1.6011799573898315, 2.3545401096343994, 1.3960000276565552], "min": [0.22885000705718997, 1.631850004196167, -1.3960000276565552], "type": "VEC3"}, {"componentType": 5123, "count": 864, "max": [463], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 464, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"componentType": 5126, "count": 464, "max": [1.62267005443573, 2.3919999599456787, 1.100000023841858], "min": [0.1932000070810318, 1.5961999893188477, -1.1100000143051147], "type": "VEC3"}, {"componentType": 5123, "count": 2304, "max": [585], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 586, "max": [0.9990389943122864, 0.9990379810333252, 1], "min": [-0.9990379810333252, -0.9990379810333252, -1], "type": "VEC3"}, {"componentType": 5126, "count": 586, "max": [0.4277999997138977, 0.4277999997138977, 1.0579999685287476], "min": [-0.4277999997138977, -0.4277999997138977, -1.0579999685287476], "type": "VEC3"}, {"componentType": 5126, "count": 586, "max": [0.9936569929122924, 0.9895756244659424], "min": [0.6050930023193359, 0.00905001163482666], "type": "VEC2"}], "materials": [{"pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0}, "name": "truck"}, {"pbrMetallicRoughness": {"baseColorFactor": [0, 0.04050629958510399, 0.021240700036287308, 1], "metallicFactor": 0}, "name": "glass"}, {"pbrMetallicRoughness": {"baseColorFactor": [0.06400000303983688, 0.06400000303983688, 0.06400000303983688, 1], "metallicFactor": 0}, "name": "window_trim"}, {"pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0}, "name": "wheels"}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 0}], "images": [{"uri": "CesiumMilkTruck.png"}], "samplers": [{"magFilter": 9729, "minFilter": 9986, "wrapS": 10497, "wrapT": 10497}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 248}, {"buffer": 0, "byteOffset": 248, "byteLength": 992}, {"buffer": 0, "byteOffset": 1240, "byteLength": 7871}, {"buffer": 0, "byteOffset": 9111, "byteLength": 474}, {"buffer": 0, "byteOffset": 9585, "byteLength": 1249}, {"buffer": 0, "byteOffset": 10834, "byteLength": 3137}], "buffers": [{"byteLength": 13971, "uri": "0.bin"}], "extensionsRequired": ["KHR_draco_mesh_compression"], "extensionsUsed": ["KHR_draco_mesh_compression"]}